"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tutor/page",{

/***/ "(app-pages-browser)/./src/components/TutorClient.tsx":
/*!****************************************!*\
  !*** ./src/components/TutorClient.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TutorClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AuthModal */ \"(app-pages-browser)/./src/components/AuthModal.tsx\");\n/* harmony import */ var _components_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MarkdownRenderer */ \"(app-pages-browser)/./src/components/MarkdownRenderer.tsx\");\n/* harmony import */ var _components_AdSense__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/AdSense */ \"(app-pages-browser)/./src/components/AdSense.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction TutorClient() {\n    _s();\n    const { language, t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const explanationTypes = [\n        {\n            id: 'grammar',\n            name: 'Grammar',\n            icon: '📝',\n            description: 'Grammar rules and structures'\n        },\n        {\n            id: 'vocabulary',\n            name: 'Vocabulary',\n            icon: '📚',\n            description: 'Word meanings and usage'\n        },\n        {\n            id: 'pronunciation',\n            name: 'Pronunciation',\n            icon: '🗣️',\n            description: 'Pronunciation and phonetics'\n        },\n        {\n            id: 'usage',\n            name: 'Usage',\n            icon: '💬',\n            description: 'Proper usage in context'\n        },\n        {\n            id: 'cultural',\n            name: 'Cultural',\n            icon: '🌍',\n            description: 'Cultural context and appropriateness'\n        },\n        {\n            id: 'general',\n            name: 'General',\n            icon: '❓',\n            description: 'General English questions'\n        }\n    ];\n    const quickQuestions = [\n        \"What's the difference between 'a' and 'an'?\",\n        \"How do I use present perfect tense?\",\n        \"When should I use 'who' vs 'whom'?\",\n        \"What are some common phrasal verbs?\",\n        \"How do I improve my pronunciation?\",\n        \"What's the difference between 'affect' and 'effect'?\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TutorClient.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"TutorClient.useEffect\"], [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TutorClient.useEffect\": ()=>{\n            // Add welcome message when component mounts\n            if (messages.length === 0) {\n                const welcomeMessage = {\n                    id: 'welcome',\n                    type: 'ai',\n                    content: \"Hello! I'm your AI English tutor. I'm here to help you with grammar, vocabulary, pronunciation, and any other English learning questions you might have. \\n\\nWhat would you like to learn about today?\",\n                    timestamp: new Date()\n                };\n                setMessages([\n                    welcomeMessage\n                ]);\n            }\n        }\n    }[\"TutorClient.useEffect\"], []);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = async (messageText)=>{\n        const text = messageText || inputMessage.trim();\n        if (!text || isLoading) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            type: 'user',\n            content: text,\n            timestamp: new Date(),\n            explanationType: selectedType\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputMessage('');\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/ai/explanation', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    question: text,\n                    language,\n                    context: '',\n                    explanationType: selectedType\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                const aiResponse = data.explanation;\n                // Format the AI response\n                let formattedResponse = \"## \".concat(aiResponse.answer, \"\\n\\n\");\n                formattedResponse += \"\".concat(aiResponse.explanation, \"\\n\\n\");\n                if (aiResponse.examples && aiResponse.examples.length > 0) {\n                    formattedResponse += \"### Examples:\\n\";\n                    aiResponse.examples.forEach((example, index)=>{\n                        formattedResponse += \"\".concat(index + 1, \". \").concat(example, \"\\n\");\n                    });\n                    formattedResponse += '\\n';\n                }\n                if (aiResponse.tips && aiResponse.tips.length > 0) {\n                    formattedResponse += \"### Tips:\\n\";\n                    aiResponse.tips.forEach((tip)=>{\n                        formattedResponse += \"\\uD83D\\uDCA1 \".concat(tip, \"\\n\");\n                    });\n                    formattedResponse += '\\n';\n                }\n                if (aiResponse.commonMistakes && aiResponse.commonMistakes.length > 0) {\n                    formattedResponse += \"### Common Mistakes to Avoid:\\n\";\n                    aiResponse.commonMistakes.forEach((mistake)=>{\n                        formattedResponse += \"⚠️ \".concat(mistake, \"\\n\");\n                    });\n                    formattedResponse += '\\n';\n                }\n                if (aiResponse.practiceExercises && aiResponse.practiceExercises.length > 0) {\n                    formattedResponse += \"### Practice Suggestions:\\n\";\n                    aiResponse.practiceExercises.forEach((exercise)=>{\n                        formattedResponse += \"\\uD83D\\uDCDD \".concat(exercise, \"\\n\");\n                    });\n                    formattedResponse += '\\n';\n                }\n                if (aiResponse.relatedTopics && aiResponse.relatedTopics.length > 0) {\n                    formattedResponse += \"### Related Topics:\\n\";\n                    aiResponse.relatedTopics.forEach((topic)=>{\n                        formattedResponse += \"\\uD83D\\uDD17 \".concat(topic, \"\\n\");\n                    });\n                }\n                const aiMessage = {\n                    id: \"ai_\".concat(Date.now()),\n                    type: 'ai',\n                    content: formattedResponse,\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiMessage\n                    ]);\n            } else {\n                throw new Error(data.error || 'Failed to get response');\n            }\n        } catch (error) {\n            console.error('Error getting AI response:', error);\n            const errorMessage = {\n                id: \"error_\".concat(Date.now()),\n                type: 'ai',\n                content: t('aiErrorMessage'),\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onAuthClick: ()=>setShowAuthModal(true),\n                showBackButton: true,\n                onBackClick: ()=>window.history.back()\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24 pb-8 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl sm:text-5xl pb-2 font-bold bg-gradient-to-r from-gray-800 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-4\",\n                                    children: t('tutorTitle')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto mb-6\",\n                                    children: t('tutorSubtitle')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-6 mb-6 shadow-xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                    children: \"What type of help do you need?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3\",\n                                    children: explanationTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedType(type.id),\n                                            className: \"p-3 rounded-xl text-center transition-all duration-300 \".concat(selectedType === type.id ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg scale-105' : 'bg-white/80 hover:bg-white text-gray-700 hover:shadow-md'),\n                                            title: type.description,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl mb-1\",\n                                                    children: type.icon\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-medium\",\n                                                    children: type.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, type.id, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        messages.length <= 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-6 mb-6 shadow-xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                    children: \"Quick Questions to Get Started:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                    children: quickQuestions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleSendMessage(question),\n                                            className: \"p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors text-sm text-gray-700\",\n                                            disabled: isLoading,\n                                            children: question\n                                        }, index, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-96 overflow-y-auto p-6 space-y-4\",\n                                    children: [\n                                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex \".concat(message.type === 'user' ? 'justify-end' : 'justify-start'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-w-[80%] p-4 rounded-2xl \".concat(message.type === 'user' ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white' : 'bg-gray-100 text-gray-800'),\n                                                    children: [\n                                                        message.type === 'ai' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"prose prose-sm max-w-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                content: message.content\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: message.content\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-2 \".concat(message.type === 'user' ? 'text-blue-100' : 'text-gray-500'),\n                                                            children: message.timestamp.toLocaleTimeString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, message.id, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this)),\n                                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-100 text-gray-800 p-4 rounded-2xl\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Thinking...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            ref: messagesEndRef\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: inputMessage,\n                                                onChange: (e)=>setInputMessage(e.target.value),\n                                                onKeyDown: handleKeyPress,\n                                                placeholder: t('askQuestion'),\n                                                className: \"flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                                                rows: 2,\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleSendMessage(),\n                                                disabled: !inputMessage.trim() || isLoading,\n                                                className: \"px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                children: t('send')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdSense__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                adSlot: \"4567890123\",\n                                adFormat: \"auto\",\n                                className: \"rounded-lg\",\n                                style: {\n                                    display: 'block',\n                                    minHeight: '100px'\n                                },\n                                lazy: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                        children: \"What You Can Ask Me\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-blue-800 mb-2\",\n                                                        children: \"\\uD83D\\uDCDD Grammar Questions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-gray-700 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Verb tenses and conjugations\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Sentence structure and syntax\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Parts of speech usage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-green-800 mb-2\",\n                                                        children: \"\\uD83D\\uDCDA Vocabulary Help\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-gray-700 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Word meanings and definitions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Synonyms and antonyms\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Usage in different contexts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-purple-800 mb-2\",\n                                                        children: \"\\uD83D\\uDDE3️ Pronunciation Guide\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-gray-700 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Phonetic transcriptions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Stress patterns\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Common pronunciation mistakes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-orange-800 mb-2\",\n                                                        children: \"\\uD83D\\uDCAC Usage & Context\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-gray-700 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Formal vs informal language\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Cultural appropriateness\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• Common expressions and idioms\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\TutorClient.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(TutorClient, \"raTWYorsxzsYRzxhAEVoJYszNyY=\", false, function() {\n    return [\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage\n    ];\n});\n_c = TutorClient;\nvar _c;\n$RefreshReg$(_c, \"TutorClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1R1dG9yQ2xpZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFb0Q7QUFDSztBQUNoQjtBQUNBO0FBQ007QUFDYztBQUNsQjtBQW9CNUIsU0FBU1M7O0lBQ3RCLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxDQUFDLEVBQUUsR0FBR1Isc0VBQVdBO0lBQ25DLE1BQU0sQ0FBQ1MsZUFBZUMsaUJBQWlCLEdBQUdiLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ2MsVUFBVUMsWUFBWSxHQUFHZiwrQ0FBUUEsQ0FBZ0IsRUFBRTtJQUMxRCxNQUFNLENBQUNnQixjQUFjQyxnQkFBZ0IsR0FBR2pCLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2tCLFdBQVdDLGFBQWEsR0FBR25CLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ29CLGNBQWNDLGdCQUFnQixHQUFHckIsK0NBQVFBLENBQUM7SUFDakQsTUFBTXNCLGlCQUFpQnJCLDZDQUFNQSxDQUFpQjtJQUU5QyxNQUFNc0IsbUJBQW1CO1FBQ3ZCO1lBQUVDLElBQUk7WUFBV0MsTUFBTTtZQUFXQyxNQUFNO1lBQU1DLGFBQWE7UUFBK0I7UUFDMUY7WUFBRUgsSUFBSTtZQUFjQyxNQUFNO1lBQWNDLE1BQU07WUFBTUMsYUFBYTtRQUEwQjtRQUMzRjtZQUFFSCxJQUFJO1lBQWlCQyxNQUFNO1lBQWlCQyxNQUFNO1lBQU9DLGFBQWE7UUFBOEI7UUFDdEc7WUFBRUgsSUFBSTtZQUFTQyxNQUFNO1lBQVNDLE1BQU07WUFBTUMsYUFBYTtRQUEwQjtRQUNqRjtZQUFFSCxJQUFJO1lBQVlDLE1BQU07WUFBWUMsTUFBTTtZQUFNQyxhQUFhO1FBQXVDO1FBQ3BHO1lBQUVILElBQUk7WUFBV0MsTUFBTTtZQUFXQyxNQUFNO1lBQUtDLGFBQWE7UUFBNEI7S0FDdkY7SUFFRCxNQUFNQyxpQkFBaUI7UUFDckI7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFFRDFCLGdEQUFTQTtpQ0FBQztZQUNSMkI7UUFDRjtnQ0FBRztRQUFDZjtLQUFTO0lBRWJaLGdEQUFTQTtpQ0FBQztZQUNSLDRDQUE0QztZQUM1QyxJQUFJWSxTQUFTZ0IsTUFBTSxLQUFLLEdBQUc7Z0JBQ3pCLE1BQU1DLGlCQUE4QjtvQkFDbENQLElBQUk7b0JBQ0pRLE1BQU07b0JBQ05DLFNBQVU7b0JBR1ZDLFdBQVcsSUFBSUM7Z0JBQ2pCO2dCQUNBcEIsWUFBWTtvQkFBQ2dCO2lCQUFlO1lBQzlCO1FBQ0Y7Z0NBQUcsRUFBRTtJQUVMLE1BQU1GLGlCQUFpQjtZQUNyQlA7U0FBQUEsMEJBQUFBLGVBQWVjLE9BQU8sY0FBdEJkLDhDQUFBQSx3QkFBd0JlLGNBQWMsQ0FBQztZQUFFQyxVQUFVO1FBQVM7SUFDOUQ7SUFFQSxNQUFNQyxvQkFBb0IsT0FBT0M7UUFDL0IsTUFBTUMsT0FBT0QsZUFBZXhCLGFBQWEwQixJQUFJO1FBQzdDLElBQUksQ0FBQ0QsUUFBUXZCLFdBQVc7UUFFeEIsTUFBTXlCLGNBQTJCO1lBQy9CbkIsSUFBSSxRQUFtQixPQUFYVyxLQUFLUyxHQUFHO1lBQ3BCWixNQUFNO1lBQ05DLFNBQVNRO1lBQ1RQLFdBQVcsSUFBSUM7WUFDZlUsaUJBQWlCekI7UUFDbkI7UUFFQUwsWUFBWStCLENBQUFBLE9BQVE7bUJBQUlBO2dCQUFNSDthQUFZO1FBQzFDMUIsZ0JBQWdCO1FBQ2hCRSxhQUFhO1FBRWIsSUFBSTtZQUNGLE1BQU00QixXQUFXLE1BQU1DLE1BQU0sdUJBQXVCO2dCQUNsREMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CQyxVQUFVYjtvQkFDVi9CO29CQUNBNkMsU0FBUztvQkFDVFYsaUJBQWlCekI7Z0JBQ25CO1lBQ0Y7WUFFQSxNQUFNb0MsT0FBTyxNQUFNVCxTQUFTVSxJQUFJO1lBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtnQkFDaEIsTUFBTUMsYUFBeUJILEtBQUtJLFdBQVc7Z0JBRS9DLHlCQUF5QjtnQkFDekIsSUFBSUMsb0JBQW9CLE1BQXdCLE9BQWxCRixXQUFXRyxNQUFNLEVBQUM7Z0JBQ2hERCxxQkFBcUIsR0FBMEIsT0FBdkJGLFdBQVdDLFdBQVcsRUFBQztnQkFFL0MsSUFBSUQsV0FBV0ksUUFBUSxJQUFJSixXQUFXSSxRQUFRLENBQUNqQyxNQUFNLEdBQUcsR0FBRztvQkFDekQrQixxQkFBc0I7b0JBQ3RCRixXQUFXSSxRQUFRLENBQUNDLE9BQU8sQ0FBQyxDQUFDQyxTQUFTQzt3QkFDcENMLHFCQUFxQixHQUFpQkksT0FBZEMsUUFBUSxHQUFFLE1BQVksT0FBUkQsU0FBUTtvQkFDaEQ7b0JBQ0FKLHFCQUFxQjtnQkFDdkI7Z0JBRUEsSUFBSUYsV0FBV1EsSUFBSSxJQUFJUixXQUFXUSxJQUFJLENBQUNyQyxNQUFNLEdBQUcsR0FBRztvQkFDakQrQixxQkFBc0I7b0JBQ3RCRixXQUFXUSxJQUFJLENBQUNILE9BQU8sQ0FBQyxDQUFDSTt3QkFDdkJQLHFCQUFxQixnQkFBVSxPQUFKTyxLQUFJO29CQUNqQztvQkFDQVAscUJBQXFCO2dCQUN2QjtnQkFFQSxJQUFJRixXQUFXVSxjQUFjLElBQUlWLFdBQVdVLGNBQWMsQ0FBQ3ZDLE1BQU0sR0FBRyxHQUFHO29CQUNyRStCLHFCQUFzQjtvQkFDdEJGLFdBQVdVLGNBQWMsQ0FBQ0wsT0FBTyxDQUFDLENBQUNNO3dCQUNqQ1QscUJBQXFCLE1BQWMsT0FBUlMsU0FBUTtvQkFDckM7b0JBQ0FULHFCQUFxQjtnQkFDdkI7Z0JBRUEsSUFBSUYsV0FBV1ksaUJBQWlCLElBQUlaLFdBQVdZLGlCQUFpQixDQUFDekMsTUFBTSxHQUFHLEdBQUc7b0JBQzNFK0IscUJBQXNCO29CQUN0QkYsV0FBV1ksaUJBQWlCLENBQUNQLE9BQU8sQ0FBQyxDQUFDUTt3QkFDcENYLHFCQUFxQixnQkFBZSxPQUFUVyxVQUFTO29CQUN0QztvQkFDQVgscUJBQXFCO2dCQUN2QjtnQkFFQSxJQUFJRixXQUFXYyxhQUFhLElBQUlkLFdBQVdjLGFBQWEsQ0FBQzNDLE1BQU0sR0FBRyxHQUFHO29CQUNuRStCLHFCQUFzQjtvQkFDdEJGLFdBQVdjLGFBQWEsQ0FBQ1QsT0FBTyxDQUFDLENBQUNVO3dCQUNoQ2IscUJBQXFCLGdCQUFZLE9BQU5hLE9BQU07b0JBQ25DO2dCQUNGO2dCQUVBLE1BQU1DLFlBQXlCO29CQUM3Qm5ELElBQUksTUFBaUIsT0FBWFcsS0FBS1MsR0FBRztvQkFDbEJaLE1BQU07b0JBQ05DLFNBQVM0QjtvQkFDVDNCLFdBQVcsSUFBSUM7Z0JBQ2pCO2dCQUVBcEIsWUFBWStCLENBQUFBLE9BQVE7MkJBQUlBO3dCQUFNNkI7cUJBQVU7WUFDMUMsT0FBTztnQkFDTCxNQUFNLElBQUlDLE1BQU1wQixLQUFLcUIsS0FBSyxJQUFJO1lBQ2hDO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1lBQzVDLE1BQU1FLGVBQTRCO2dCQUNoQ3ZELElBQUksU0FBb0IsT0FBWFcsS0FBS1MsR0FBRztnQkFDckJaLE1BQU07Z0JBQ05DLFNBQVN0QixFQUFFO2dCQUNYdUIsV0FBVyxJQUFJQztZQUNqQjtZQUNBcEIsWUFBWStCLENBQUFBLE9BQVE7dUJBQUlBO29CQUFNaUM7aUJBQWE7UUFDN0MsU0FBVTtZQUNSNUQsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNNkQsaUJBQWlCLENBQUNDO1FBQ3RCLElBQUlBLEVBQUVDLEdBQUcsS0FBSyxXQUFXLENBQUNELEVBQUVFLFFBQVEsRUFBRTtZQUNwQ0YsRUFBRUcsY0FBYztZQUNoQjdDO1FBQ0Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDOEM7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNsRiwwREFBTUE7Z0JBQ0xtRixhQUFhLElBQU0xRSxpQkFBaUI7Z0JBQ3BDMkUsZ0JBQWdCO2dCQUNoQkMsYUFBYSxJQUFNQyxPQUFPQyxPQUFPLENBQUNDLElBQUk7Ozs7OzswQkFHeEMsOERBQUNQO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ087b0NBQUdQLFdBQVU7OENBQ1gzRSxFQUFFOzs7Ozs7OENBRUwsOERBQUNtRjtvQ0FBRVIsV0FBVTs4Q0FDVjNFLEVBQUU7Ozs7Ozs7Ozs7OztzQ0FLUCw4REFBQzBFOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ1M7b0NBQUdULFdBQVU7OENBQTJDOzs7Ozs7OENBQ3pELDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWi9ELGlCQUFpQnlFLEdBQUcsQ0FBQyxDQUFDaEUscUJBQ3JCLDhEQUFDaUU7NENBRUNDLFNBQVMsSUFBTTdFLGdCQUFnQlcsS0FBS1IsRUFBRTs0Q0FDdEM4RCxXQUFXLDBEQUlWLE9BSENsRSxpQkFBaUJZLEtBQUtSLEVBQUUsR0FDcEIsZ0ZBQ0E7NENBRU4yRSxPQUFPbkUsS0FBS0wsV0FBVzs7OERBRXZCLDhEQUFDMEQ7b0RBQUlDLFdBQVU7OERBQWdCdEQsS0FBS04sSUFBSTs7Ozs7OzhEQUN4Qyw4REFBQzJEO29EQUFJQyxXQUFVOzhEQUF1QnRELEtBQUtQLElBQUk7Ozs7Ozs7MkNBVjFDTyxLQUFLUixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3dCQWlCbkJWLFNBQVNnQixNQUFNLElBQUksbUJBQ2xCLDhEQUFDdUQ7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDUztvQ0FBR1QsV0FBVTs4Q0FBMkM7Ozs7Ozs4Q0FDekQsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaMUQsZUFBZW9FLEdBQUcsQ0FBQyxDQUFDMUMsVUFBVVksc0JBQzdCLDhEQUFDK0I7NENBRUNDLFNBQVMsSUFBTTNELGtCQUFrQmU7NENBQ2pDZ0MsV0FBVTs0Q0FDVmMsVUFBVWxGO3NEQUVUb0M7MkNBTElZOzs7Ozs7Ozs7Ozs7Ozs7O3NDQWFmLDhEQUFDbUI7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7d0NBQ1p4RSxTQUFTa0YsR0FBRyxDQUFDLENBQUNLLHdCQUNiLDhEQUFDaEI7Z0RBRUNDLFdBQVcsUUFBa0UsT0FBMURlLFFBQVFyRSxJQUFJLEtBQUssU0FBUyxnQkFBZ0I7MERBRTdELDRFQUFDcUQ7b0RBQ0NDLFdBQVcsK0JBSVYsT0FIQ2UsUUFBUXJFLElBQUksS0FBSyxTQUNiLDREQUNBOzt3REFHTHFFLFFBQVFyRSxJQUFJLEtBQUsscUJBQ2hCLDhEQUFDcUQ7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUMvRSxvRUFBZ0JBO2dFQUFDMEIsU0FBU29FLFFBQVFwRSxPQUFPOzs7Ozs7Ozs7O2lGQUc1Qyw4REFBQzZEOzREQUFFUixXQUFVO3NFQUFXZSxRQUFRcEUsT0FBTzs7Ozs7O3NFQUV6Qyw4REFBQ29EOzREQUFJQyxXQUFXLGdCQUVmLE9BRENlLFFBQVFyRSxJQUFJLEtBQUssU0FBUyxrQkFBa0I7c0VBRTNDcUUsUUFBUW5FLFNBQVMsQ0FBQ29FLGtCQUFrQjs7Ozs7Ozs7Ozs7OytDQXBCcENELFFBQVE3RSxFQUFFOzs7Ozt3Q0EwQmxCTiwyQkFDQyw4REFBQ21FOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7Ozs7O3NFQUNmLDhEQUFDaUI7NERBQUtqQixXQUFVO3NFQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU1sQyw4REFBQ0Q7NENBQUltQixLQUFLbEY7Ozs7Ozs7Ozs7Ozs4Q0FJWiw4REFBQytEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNtQjtnREFDQ0MsT0FBTzFGO2dEQUNQMkYsVUFBVSxDQUFDMUIsSUFBTWhFLGdCQUFnQmdFLEVBQUUyQixNQUFNLENBQUNGLEtBQUs7Z0RBQy9DRyxXQUFXN0I7Z0RBQ1g4QixhQUFhbkcsRUFBRTtnREFDZjJFLFdBQVU7Z0RBQ1Z5QixNQUFNO2dEQUNOWCxVQUFVbEY7Ozs7OzswREFFWiw4REFBQytFO2dEQUNDQyxTQUFTLElBQU0zRDtnREFDZjZELFVBQVUsQ0FBQ3BGLGFBQWEwQixJQUFJLE1BQU14QjtnREFDbENvRSxXQUFVOzBEQUVUM0UsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT1gsOERBQUMwRTs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQzlFLDJEQUFPQTtnQ0FDTndHLFFBQU87Z0NBQ1BDLFVBQVM7Z0NBQ1QzQixXQUFVO2dDQUNWNEIsT0FBTztvQ0FBRUMsU0FBUztvQ0FBU0MsV0FBVztnQ0FBUTtnQ0FDOUNDLE1BQU07Ozs7Ozs7Ozs7O3NDQUtWLDhEQUFDaEM7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ1M7d0NBQUdULFdBQVU7a0RBQTJDOzs7Ozs7a0RBQ3pELDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEOztrRUFDQyw4REFBQ2lDO3dEQUFHaEMsV0FBVTtrRUFBaUM7Ozs7OztrRUFDL0MsOERBQUNpQzt3REFBR2pDLFdBQVU7OzBFQUNaLDhEQUFDa0M7MEVBQUc7Ozs7OzswRUFDSiw4REFBQ0E7MEVBQUc7Ozs7OzswRUFDSiw4REFBQ0E7MEVBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFHUiw4REFBQ25DOztrRUFDQyw4REFBQ2lDO3dEQUFHaEMsV0FBVTtrRUFBa0M7Ozs7OztrRUFDaEQsOERBQUNpQzt3REFBR2pDLFdBQVU7OzBFQUNaLDhEQUFDa0M7MEVBQUc7Ozs7OzswRUFDSiw4REFBQ0E7MEVBQUc7Ozs7OzswRUFDSiw4REFBQ0E7MEVBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFHUiw4REFBQ25DOztrRUFDQyw4REFBQ2lDO3dEQUFHaEMsV0FBVTtrRUFBbUM7Ozs7OztrRUFDakQsOERBQUNpQzt3REFBR2pDLFdBQVU7OzBFQUNaLDhEQUFDa0M7MEVBQUc7Ozs7OzswRUFDSiw4REFBQ0E7MEVBQUc7Ozs7OzswRUFDSiw4REFBQ0E7MEVBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFHUiw4REFBQ25DOztrRUFDQyw4REFBQ2lDO3dEQUFHaEMsV0FBVTtrRUFBbUM7Ozs7OztrRUFDakQsOERBQUNpQzt3REFBR2pDLFdBQVU7OzBFQUNaLDhEQUFDa0M7MEVBQUc7Ozs7OzswRUFDSiw4REFBQ0E7MEVBQUc7Ozs7OzswRUFDSiw4REFBQ0E7MEVBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU2xCLDhEQUFDbEgsNkRBQVNBO2dCQUNSbUgsUUFBUTdHO2dCQUNSOEcsU0FBUyxJQUFNN0csaUJBQWlCOzs7Ozs7MEJBR2xDLDhEQUFDUiwwREFBTUE7Ozs7Ozs7Ozs7O0FBR2I7R0EvVndCSTs7UUFDRU4sa0VBQVdBOzs7S0FEYk0iLCJzb3VyY2VzIjpbIkQ6XFxBSVxcd2Vibmdvbm5ndVxcaW5maW5pdGUtbGFuZ3VhZ2VcXHNyY1xcY29tcG9uZW50c1xcVHV0b3JDbGllbnQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlTGFuZ3VhZ2UgfSBmcm9tICdAL2NvbnRleHRzL0xhbmd1YWdlQ29udGV4dCc7XG5pbXBvcnQgSGVhZGVyIGZyb20gJ0AvY29tcG9uZW50cy9IZWFkZXInO1xuaW1wb3J0IEZvb3RlciBmcm9tICdAL2NvbXBvbmVudHMvRm9vdGVyJztcbmltcG9ydCBBdXRoTW9kYWwgZnJvbSAnQC9jb21wb25lbnRzL0F1dGhNb2RhbCc7XG5pbXBvcnQgTWFya2Rvd25SZW5kZXJlciBmcm9tICdAL2NvbXBvbmVudHMvTWFya2Rvd25SZW5kZXJlcic7XG5pbXBvcnQgQWRTZW5zZSBmcm9tICdAL2NvbXBvbmVudHMvQWRTZW5zZSc7XG5cbmludGVyZmFjZSBDaGF0TWVzc2FnZSB7XG4gIGlkOiBzdHJpbmc7XG4gIHR5cGU6ICd1c2VyJyB8ICdhaSc7XG4gIGNvbnRlbnQ6IHN0cmluZztcbiAgdGltZXN0YW1wOiBEYXRlO1xuICBleHBsYW5hdGlvblR5cGU/OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBBSVJlc3BvbnNlIHtcbiAgYW5zd2VyOiBzdHJpbmc7XG4gIGV4cGxhbmF0aW9uOiBzdHJpbmc7XG4gIGV4YW1wbGVzOiBzdHJpbmdbXTtcbiAgdGlwczogc3RyaW5nW107XG4gIHJlbGF0ZWRUb3BpY3M6IHN0cmluZ1tdO1xuICBjb21tb25NaXN0YWtlczogc3RyaW5nW107XG4gIHByYWN0aWNlRXhlcmNpc2VzOiBzdHJpbmdbXTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVHV0b3JDbGllbnQoKSB7XG4gIGNvbnN0IHsgbGFuZ3VhZ2UsIHQgfSA9IHVzZUxhbmd1YWdlKCk7XG4gIGNvbnN0IFtzaG93QXV0aE1vZGFsLCBzZXRTaG93QXV0aE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW21lc3NhZ2VzLCBzZXRNZXNzYWdlc10gPSB1c2VTdGF0ZTxDaGF0TWVzc2FnZVtdPihbXSk7XG4gIGNvbnN0IFtpbnB1dE1lc3NhZ2UsIHNldElucHV0TWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzZWxlY3RlZFR5cGUsIHNldFNlbGVjdGVkVHlwZV0gPSB1c2VTdGF0ZSgnZ2VuZXJhbCcpO1xuICBjb25zdCBtZXNzYWdlc0VuZFJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG5cbiAgY29uc3QgZXhwbGFuYXRpb25UeXBlcyA9IFtcbiAgICB7IGlkOiAnZ3JhbW1hcicsIG5hbWU6ICdHcmFtbWFyJywgaWNvbjogJ/Cfk50nLCBkZXNjcmlwdGlvbjogJ0dyYW1tYXIgcnVsZXMgYW5kIHN0cnVjdHVyZXMnIH0sXG4gICAgeyBpZDogJ3ZvY2FidWxhcnknLCBuYW1lOiAnVm9jYWJ1bGFyeScsIGljb246ICfwn5OaJywgZGVzY3JpcHRpb246ICdXb3JkIG1lYW5pbmdzIGFuZCB1c2FnZScgfSxcbiAgICB7IGlkOiAncHJvbnVuY2lhdGlvbicsIG5hbWU6ICdQcm9udW5jaWF0aW9uJywgaWNvbjogJ/Cfl6PvuI8nLCBkZXNjcmlwdGlvbjogJ1Byb251bmNpYXRpb24gYW5kIHBob25ldGljcycgfSxcbiAgICB7IGlkOiAndXNhZ2UnLCBuYW1lOiAnVXNhZ2UnLCBpY29uOiAn8J+SrCcsIGRlc2NyaXB0aW9uOiAnUHJvcGVyIHVzYWdlIGluIGNvbnRleHQnIH0sXG4gICAgeyBpZDogJ2N1bHR1cmFsJywgbmFtZTogJ0N1bHR1cmFsJywgaWNvbjogJ/CfjI0nLCBkZXNjcmlwdGlvbjogJ0N1bHR1cmFsIGNvbnRleHQgYW5kIGFwcHJvcHJpYXRlbmVzcycgfSxcbiAgICB7IGlkOiAnZ2VuZXJhbCcsIG5hbWU6ICdHZW5lcmFsJywgaWNvbjogJ+KdkycsIGRlc2NyaXB0aW9uOiAnR2VuZXJhbCBFbmdsaXNoIHF1ZXN0aW9ucycgfVxuICBdO1xuXG4gIGNvbnN0IHF1aWNrUXVlc3Rpb25zID0gW1xuICAgIFwiV2hhdCdzIHRoZSBkaWZmZXJlbmNlIGJldHdlZW4gJ2EnIGFuZCAnYW4nP1wiLFxuICAgIFwiSG93IGRvIEkgdXNlIHByZXNlbnQgcGVyZmVjdCB0ZW5zZT9cIixcbiAgICBcIldoZW4gc2hvdWxkIEkgdXNlICd3aG8nIHZzICd3aG9tJz9cIixcbiAgICBcIldoYXQgYXJlIHNvbWUgY29tbW9uIHBocmFzYWwgdmVyYnM/XCIsXG4gICAgXCJIb3cgZG8gSSBpbXByb3ZlIG15IHByb251bmNpYXRpb24/XCIsXG4gICAgXCJXaGF0J3MgdGhlIGRpZmZlcmVuY2UgYmV0d2VlbiAnYWZmZWN0JyBhbmQgJ2VmZmVjdCc/XCJcbiAgXTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNjcm9sbFRvQm90dG9tKCk7XG4gIH0sIFttZXNzYWdlc10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gQWRkIHdlbGNvbWUgbWVzc2FnZSB3aGVuIGNvbXBvbmVudCBtb3VudHNcbiAgICBpZiAobWVzc2FnZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICBjb25zdCB3ZWxjb21lTWVzc2FnZTogQ2hhdE1lc3NhZ2UgPSB7XG4gICAgICAgIGlkOiAnd2VsY29tZScsXG4gICAgICAgIHR5cGU6ICdhaScsXG4gICAgICAgIGNvbnRlbnQ6IGBIZWxsbyEgSSdtIHlvdXIgQUkgRW5nbGlzaCB0dXRvci4gSSdtIGhlcmUgdG8gaGVscCB5b3Ugd2l0aCBncmFtbWFyLCB2b2NhYnVsYXJ5LCBwcm9udW5jaWF0aW9uLCBhbmQgYW55IG90aGVyIEVuZ2xpc2ggbGVhcm5pbmcgcXVlc3Rpb25zIHlvdSBtaWdodCBoYXZlLiBcblxuV2hhdCB3b3VsZCB5b3UgbGlrZSB0byBsZWFybiBhYm91dCB0b2RheT9gLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKClcbiAgICAgIH07XG4gICAgICBzZXRNZXNzYWdlcyhbd2VsY29tZU1lc3NhZ2VdKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICBjb25zdCBzY3JvbGxUb0JvdHRvbSA9ICgpID0+IHtcbiAgICBtZXNzYWdlc0VuZFJlZi5jdXJyZW50Py5zY3JvbGxJbnRvVmlldyh7IGJlaGF2aW9yOiAnc21vb3RoJyB9KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTZW5kTWVzc2FnZSA9IGFzeW5jIChtZXNzYWdlVGV4dD86IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHRleHQgPSBtZXNzYWdlVGV4dCB8fCBpbnB1dE1lc3NhZ2UudHJpbSgpO1xuICAgIGlmICghdGV4dCB8fCBpc0xvYWRpbmcpIHJldHVybjtcblxuICAgIGNvbnN0IHVzZXJNZXNzYWdlOiBDaGF0TWVzc2FnZSA9IHtcbiAgICAgIGlkOiBgdXNlcl8ke0RhdGUubm93KCl9YCxcbiAgICAgIHR5cGU6ICd1c2VyJyxcbiAgICAgIGNvbnRlbnQ6IHRleHQsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICBleHBsYW5hdGlvblR5cGU6IHNlbGVjdGVkVHlwZVxuICAgIH07XG5cbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCB1c2VyTWVzc2FnZV0pO1xuICAgIHNldElucHV0TWVzc2FnZSgnJyk7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYWkvZXhwbGFuYXRpb24nLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHF1ZXN0aW9uOiB0ZXh0LFxuICAgICAgICAgIGxhbmd1YWdlLFxuICAgICAgICAgIGNvbnRleHQ6ICcnLFxuICAgICAgICAgIGV4cGxhbmF0aW9uVHlwZTogc2VsZWN0ZWRUeXBlXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgY29uc3QgYWlSZXNwb25zZTogQUlSZXNwb25zZSA9IGRhdGEuZXhwbGFuYXRpb247XG4gICAgICAgIFxuICAgICAgICAvLyBGb3JtYXQgdGhlIEFJIHJlc3BvbnNlXG4gICAgICAgIGxldCBmb3JtYXR0ZWRSZXNwb25zZSA9IGAjIyAke2FpUmVzcG9uc2UuYW5zd2VyfVxcblxcbmA7XG4gICAgICAgIGZvcm1hdHRlZFJlc3BvbnNlICs9IGAke2FpUmVzcG9uc2UuZXhwbGFuYXRpb259XFxuXFxuYDtcbiAgICAgICAgXG4gICAgICAgIGlmIChhaVJlc3BvbnNlLmV4YW1wbGVzICYmIGFpUmVzcG9uc2UuZXhhbXBsZXMubGVuZ3RoID4gMCkge1xuICAgICAgICAgIGZvcm1hdHRlZFJlc3BvbnNlICs9IGAjIyMgRXhhbXBsZXM6XFxuYDtcbiAgICAgICAgICBhaVJlc3BvbnNlLmV4YW1wbGVzLmZvckVhY2goKGV4YW1wbGUsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICBmb3JtYXR0ZWRSZXNwb25zZSArPSBgJHtpbmRleCArIDF9LiAke2V4YW1wbGV9XFxuYDtcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBmb3JtYXR0ZWRSZXNwb25zZSArPSAnXFxuJztcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChhaVJlc3BvbnNlLnRpcHMgJiYgYWlSZXNwb25zZS50aXBzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBmb3JtYXR0ZWRSZXNwb25zZSArPSBgIyMjIFRpcHM6XFxuYDtcbiAgICAgICAgICBhaVJlc3BvbnNlLnRpcHMuZm9yRWFjaCgodGlwKSA9PiB7XG4gICAgICAgICAgICBmb3JtYXR0ZWRSZXNwb25zZSArPSBg8J+SoSAke3RpcH1cXG5gO1xuICAgICAgICAgIH0pO1xuICAgICAgICAgIGZvcm1hdHRlZFJlc3BvbnNlICs9ICdcXG4nO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGFpUmVzcG9uc2UuY29tbW9uTWlzdGFrZXMgJiYgYWlSZXNwb25zZS5jb21tb25NaXN0YWtlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgZm9ybWF0dGVkUmVzcG9uc2UgKz0gYCMjIyBDb21tb24gTWlzdGFrZXMgdG8gQXZvaWQ6XFxuYDtcbiAgICAgICAgICBhaVJlc3BvbnNlLmNvbW1vbk1pc3Rha2VzLmZvckVhY2goKG1pc3Rha2UpID0+IHtcbiAgICAgICAgICAgIGZvcm1hdHRlZFJlc3BvbnNlICs9IGDimqDvuI8gJHttaXN0YWtlfVxcbmA7XG4gICAgICAgICAgfSk7XG4gICAgICAgICAgZm9ybWF0dGVkUmVzcG9uc2UgKz0gJ1xcbic7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoYWlSZXNwb25zZS5wcmFjdGljZUV4ZXJjaXNlcyAmJiBhaVJlc3BvbnNlLnByYWN0aWNlRXhlcmNpc2VzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBmb3JtYXR0ZWRSZXNwb25zZSArPSBgIyMjIFByYWN0aWNlIFN1Z2dlc3Rpb25zOlxcbmA7XG4gICAgICAgICAgYWlSZXNwb25zZS5wcmFjdGljZUV4ZXJjaXNlcy5mb3JFYWNoKChleGVyY2lzZSkgPT4ge1xuICAgICAgICAgICAgZm9ybWF0dGVkUmVzcG9uc2UgKz0gYPCfk50gJHtleGVyY2lzZX1cXG5gO1xuICAgICAgICAgIH0pO1xuICAgICAgICAgIGZvcm1hdHRlZFJlc3BvbnNlICs9ICdcXG4nO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGFpUmVzcG9uc2UucmVsYXRlZFRvcGljcyAmJiBhaVJlc3BvbnNlLnJlbGF0ZWRUb3BpY3MubGVuZ3RoID4gMCkge1xuICAgICAgICAgIGZvcm1hdHRlZFJlc3BvbnNlICs9IGAjIyMgUmVsYXRlZCBUb3BpY3M6XFxuYDtcbiAgICAgICAgICBhaVJlc3BvbnNlLnJlbGF0ZWRUb3BpY3MuZm9yRWFjaCgodG9waWMpID0+IHtcbiAgICAgICAgICAgIGZvcm1hdHRlZFJlc3BvbnNlICs9IGDwn5SXICR7dG9waWN9XFxuYDtcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGFpTWVzc2FnZTogQ2hhdE1lc3NhZ2UgPSB7XG4gICAgICAgICAgaWQ6IGBhaV8ke0RhdGUubm93KCl9YCxcbiAgICAgICAgICB0eXBlOiAnYWknLFxuICAgICAgICAgIGNvbnRlbnQ6IGZvcm1hdHRlZFJlc3BvbnNlLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKVxuICAgICAgICB9O1xuXG4gICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIGFpTWVzc2FnZV0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byBnZXQgcmVzcG9uc2UnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyBBSSByZXNwb25zZTonLCBlcnJvcik7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2U6IENoYXRNZXNzYWdlID0ge1xuICAgICAgICBpZDogYGVycm9yXyR7RGF0ZS5ub3coKX1gLFxuICAgICAgICB0eXBlOiAnYWknLFxuICAgICAgICBjb250ZW50OiB0KCdhaUVycm9yTWVzc2FnZScpLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKClcbiAgICAgIH07XG4gICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCBlcnJvck1lc3NhZ2VdKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlS2V5UHJlc3MgPSAoZTogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJyAmJiAhZS5zaGlmdEtleSkge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgaGFuZGxlU2VuZE1lc3NhZ2UoKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAgdmlhLWluZGlnby01MCB0by1wdXJwbGUtNTBcIj5cbiAgICAgIDxIZWFkZXJcbiAgICAgICAgb25BdXRoQ2xpY2s9eygpID0+IHNldFNob3dBdXRoTW9kYWwodHJ1ZSl9XG4gICAgICAgIHNob3dCYWNrQnV0dG9uPXt0cnVlfVxuICAgICAgICBvbkJhY2tDbGljaz17KCkgPT4gd2luZG93Lmhpc3RvcnkuYmFjaygpfVxuICAgICAgLz5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC0yNCBwYi04IHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG9cIj5cbiAgICAgICAgICB7LyogSGVhZGVyIFNlY3Rpb24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi04XCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgc206dGV4dC01eGwgcGItMiBmb250LWJvbGQgYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyYXktODAwIHZpYS1ibHVlLTgwMCB0by1wdXJwbGUtODAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50IG1iLTRcIj5cbiAgICAgICAgICAgICAge3QoJ3R1dG9yVGl0bGUnKX1cbiAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDAgbWF4LXctM3hsIG14LWF1dG8gbWItNlwiPlxuICAgICAgICAgICAgICB7dCgndHV0b3JTdWJ0aXRsZScpfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFF1ZXN0aW9uIFR5cGUgU2VsZWN0b3IgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCBiYWNrZHJvcC1ibHVyLXhsIHJvdW5kZWQtMnhsIHAtNiBtYi02IHNoYWRvdy14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDAgbWItNFwiPldoYXQgdHlwZSBvZiBoZWxwIGRvIHlvdSBuZWVkPzwvaDM+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTMgbGc6Z3JpZC1jb2xzLTYgZ2FwLTNcIj5cbiAgICAgICAgICAgICAge2V4cGxhbmF0aW9uVHlwZXMubWFwKCh0eXBlKSA9PiAoXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAga2V5PXt0eXBlLmlkfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRUeXBlKHR5cGUuaWQpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0zIHJvdW5kZWQteGwgdGV4dC1jZW50ZXIgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkVHlwZSA9PT0gdHlwZS5pZFxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1wdXJwbGUtNjAwIHRleHQtd2hpdGUgc2hhZG93LWxnIHNjYWxlLTEwNSdcbiAgICAgICAgICAgICAgICAgICAgICA6ICdiZy13aGl0ZS84MCBob3ZlcjpiZy13aGl0ZSB0ZXh0LWdyYXktNzAwIGhvdmVyOnNoYWRvdy1tZCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgdGl0bGU9e3R5cGUuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhsIG1iLTFcIj57dHlwZS5pY29ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtXCI+e3R5cGUubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBRdWljayBRdWVzdGlvbnMgKi99XG4gICAgICAgICAge21lc3NhZ2VzLmxlbmd0aCA8PSAxICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci14bCByb3VuZGVkLTJ4bCBwLTYgbWItNiBzaGFkb3cteGwgYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDAgbWItNFwiPlF1aWNrIFF1ZXN0aW9ucyB0byBHZXQgU3RhcnRlZDo8L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTNcIj5cbiAgICAgICAgICAgICAgICB7cXVpY2tRdWVzdGlvbnMubWFwKChxdWVzdGlvbiwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlU2VuZE1lc3NhZ2UocXVlc3Rpb24pfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTMgdGV4dC1sZWZ0IGJnLWdyYXktNTAgaG92ZXI6YmctZ3JheS0xMDAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyB0ZXh0LXNtIHRleHQtZ3JheS03MDBcIlxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7cXVlc3Rpb259XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgey8qIENoYXQgTWVzc2FnZXMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCBiYWNrZHJvcC1ibHVyLXhsIHJvdW5kZWQtMnhsIHNoYWRvdy14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIG1iLTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC05NiBvdmVyZmxvdy15LWF1dG8gcC02IHNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICB7bWVzc2FnZXMubWFwKChtZXNzYWdlKSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAga2V5PXttZXNzYWdlLmlkfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCAke21lc3NhZ2UudHlwZSA9PT0gJ3VzZXInID8gJ2p1c3RpZnktZW5kJyA6ICdqdXN0aWZ5LXN0YXJ0J31gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgbWF4LXctWzgwJV0gcC00IHJvdW5kZWQtMnhsICR7XG4gICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZS50eXBlID09PSAndXNlcidcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1wdXJwbGUtNjAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwJ1xuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge21lc3NhZ2UudHlwZSA9PT0gJ2FpJyA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInByb3NlIHByb3NlLXNtIG1heC13LW5vbmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxNYXJrZG93blJlbmRlcmVyIGNvbnRlbnQ9e21lc3NhZ2UuY29udGVudH0gLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e21lc3NhZ2UuY29udGVudH08L3A+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC14cyBtdC0yICR7XG4gICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZS50eXBlID09PSAndXNlcicgPyAndGV4dC1ibHVlLTEwMCcgOiAndGV4dC1ncmF5LTUwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgIHttZXNzYWdlLnRpbWVzdGFtcC50b0xvY2FsZVRpbWVTdHJpbmcoKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7aXNMb2FkaW5nICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1zdGFydFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwIHAtNCByb3VuZGVkLTJ4bFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTQgdy00IGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNTAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPlRoaW5raW5nLi4uPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdiByZWY9e21lc3NhZ2VzRW5kUmVmfSAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBJbnB1dCBBcmVhICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItZ3JheS0yMDAgcC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtpbnB1dE1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldElucHV0TWVzc2FnZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBvbktleURvd249e2hhbmRsZUtleVByZXNzfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e3QoJ2Fza1F1ZXN0aW9uJyl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgcC0zIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgcmVzaXplLW5vbmVcIlxuICAgICAgICAgICAgICAgICAgcm93cz17Mn1cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTZW5kTWVzc2FnZSgpfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFpbnB1dE1lc3NhZ2UudHJpbSgpIHx8IGlzTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMyBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8tcHVycGxlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6ZnJvbS1ibHVlLTYwMCBob3Zlcjp0by1wdXJwbGUtNzAwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7dCgnc2VuZCcpfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEFkU2Vuc2UgQmFubmVyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggbXQtOFwiPlxuICAgICAgICAgICAgPEFkU2Vuc2VcbiAgICAgICAgICAgICAgYWRTbG90PVwiNDU2Nzg5MDEyM1wiXG4gICAgICAgICAgICAgIGFkRm9ybWF0PVwiYXV0b1wiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQtbGdcIlxuICAgICAgICAgICAgICBzdHlsZT17eyBkaXNwbGF5OiAnYmxvY2snLCBtaW5IZWlnaHQ6ICcxMDBweCcgfX1cbiAgICAgICAgICAgICAgbGF6eT17dHJ1ZX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRWR1Y2F0aW9uYWwgQ29udGVudCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IG10LThcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci14bCByb3VuZGVkLTJ4bCBwLTYgc2hhZG93LXhsIGJvcmRlciBib3JkZXItd2hpdGUvMjBcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTRcIj5XaGF0IFlvdSBDYW4gQXNrIE1lPC9oMz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00IHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtYmx1ZS04MDAgbWItMlwiPvCfk50gR3JhbW1hciBRdWVzdGlvbnM8L2g0PlxuICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxsaT7igKIgVmVyYiB0ZW5zZXMgYW5kIGNvbmp1Z2F0aW9uczwvbGk+XG4gICAgICAgICAgICAgICAgICAgIDxsaT7igKIgU2VudGVuY2Ugc3RydWN0dXJlIGFuZCBzeW50YXg8L2xpPlxuICAgICAgICAgICAgICAgICAgICA8bGk+4oCiIFBhcnRzIG9mIHNwZWVjaCB1c2FnZTwvbGk+XG4gICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmVlbi04MDAgbWItMlwiPvCfk5ogVm9jYWJ1bGFyeSBIZWxwPC9oND5cbiAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgICA8bGk+4oCiIFdvcmQgbWVhbmluZ3MgYW5kIGRlZmluaXRpb25zPC9saT5cbiAgICAgICAgICAgICAgICAgICAgPGxpPuKAoiBTeW5vbnltcyBhbmQgYW50b255bXM8L2xpPlxuICAgICAgICAgICAgICAgICAgICA8bGk+4oCiIFVzYWdlIGluIGRpZmZlcmVudCBjb250ZXh0czwvbGk+XG4gICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1wdXJwbGUtODAwIG1iLTJcIj7wn5ej77iPIFByb251bmNpYXRpb24gR3VpZGU8L2g0PlxuICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxsaT7igKIgUGhvbmV0aWMgdHJhbnNjcmlwdGlvbnM8L2xpPlxuICAgICAgICAgICAgICAgICAgICA8bGk+4oCiIFN0cmVzcyBwYXR0ZXJuczwvbGk+XG4gICAgICAgICAgICAgICAgICAgIDxsaT7igKIgQ29tbW9uIHByb251bmNpYXRpb24gbWlzdGFrZXM8L2xpPlxuICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtb3JhbmdlLTgwMCBtYi0yXCI+8J+SrCBVc2FnZSAmIENvbnRleHQ8L2g0PlxuICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxsaT7igKIgRm9ybWFsIHZzIGluZm9ybWFsIGxhbmd1YWdlPC9saT5cbiAgICAgICAgICAgICAgICAgICAgPGxpPuKAoiBDdWx0dXJhbCBhcHByb3ByaWF0ZW5lc3M8L2xpPlxuICAgICAgICAgICAgICAgICAgICA8bGk+4oCiIENvbW1vbiBleHByZXNzaW9ucyBhbmQgaWRpb21zPC9saT5cbiAgICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxBdXRoTW9kYWxcbiAgICAgICAgaXNPcGVuPXtzaG93QXV0aE1vZGFsfVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93QXV0aE1vZGFsKGZhbHNlKX1cbiAgICAgIC8+XG5cbiAgICAgIDxGb290ZXIgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsInVzZUxhbmd1YWdlIiwiSGVhZGVyIiwiRm9vdGVyIiwiQXV0aE1vZGFsIiwiTWFya2Rvd25SZW5kZXJlciIsIkFkU2Vuc2UiLCJUdXRvckNsaWVudCIsImxhbmd1YWdlIiwidCIsInNob3dBdXRoTW9kYWwiLCJzZXRTaG93QXV0aE1vZGFsIiwibWVzc2FnZXMiLCJzZXRNZXNzYWdlcyIsImlucHV0TWVzc2FnZSIsInNldElucHV0TWVzc2FnZSIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsInNlbGVjdGVkVHlwZSIsInNldFNlbGVjdGVkVHlwZSIsIm1lc3NhZ2VzRW5kUmVmIiwiZXhwbGFuYXRpb25UeXBlcyIsImlkIiwibmFtZSIsImljb24iLCJkZXNjcmlwdGlvbiIsInF1aWNrUXVlc3Rpb25zIiwic2Nyb2xsVG9Cb3R0b20iLCJsZW5ndGgiLCJ3ZWxjb21lTWVzc2FnZSIsInR5cGUiLCJjb250ZW50IiwidGltZXN0YW1wIiwiRGF0ZSIsImN1cnJlbnQiLCJzY3JvbGxJbnRvVmlldyIsImJlaGF2aW9yIiwiaGFuZGxlU2VuZE1lc3NhZ2UiLCJtZXNzYWdlVGV4dCIsInRleHQiLCJ0cmltIiwidXNlck1lc3NhZ2UiLCJub3ciLCJleHBsYW5hdGlvblR5cGUiLCJwcmV2IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInF1ZXN0aW9uIiwiY29udGV4dCIsImRhdGEiLCJqc29uIiwic3VjY2VzcyIsImFpUmVzcG9uc2UiLCJleHBsYW5hdGlvbiIsImZvcm1hdHRlZFJlc3BvbnNlIiwiYW5zd2VyIiwiZXhhbXBsZXMiLCJmb3JFYWNoIiwiZXhhbXBsZSIsImluZGV4IiwidGlwcyIsInRpcCIsImNvbW1vbk1pc3Rha2VzIiwibWlzdGFrZSIsInByYWN0aWNlRXhlcmNpc2VzIiwiZXhlcmNpc2UiLCJyZWxhdGVkVG9waWNzIiwidG9waWMiLCJhaU1lc3NhZ2UiLCJFcnJvciIsImVycm9yIiwiY29uc29sZSIsImVycm9yTWVzc2FnZSIsImhhbmRsZUtleVByZXNzIiwiZSIsImtleSIsInNoaWZ0S2V5IiwicHJldmVudERlZmF1bHQiLCJkaXYiLCJjbGFzc05hbWUiLCJvbkF1dGhDbGljayIsInNob3dCYWNrQnV0dG9uIiwib25CYWNrQ2xpY2siLCJ3aW5kb3ciLCJoaXN0b3J5IiwiYmFjayIsImgxIiwicCIsImgzIiwibWFwIiwiYnV0dG9uIiwib25DbGljayIsInRpdGxlIiwiZGlzYWJsZWQiLCJtZXNzYWdlIiwidG9Mb2NhbGVUaW1lU3RyaW5nIiwic3BhbiIsInJlZiIsInRleHRhcmVhIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsIm9uS2V5RG93biIsInBsYWNlaG9sZGVyIiwicm93cyIsImFkU2xvdCIsImFkRm9ybWF0Iiwic3R5bGUiLCJkaXNwbGF5IiwibWluSGVpZ2h0IiwibGF6eSIsImg0IiwidWwiLCJsaSIsImlzT3BlbiIsIm9uQ2xvc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TutorClient.tsx\n"));

/***/ })

});