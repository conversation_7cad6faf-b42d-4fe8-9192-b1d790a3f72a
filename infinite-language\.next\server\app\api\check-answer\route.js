/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/check-answer/route";
exports.ids = ["app/api/check-answer/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcheck-answer%2Froute&page=%2Fapi%2Fcheck-answer%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcheck-answer%2Froute.ts&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcheck-answer%2Froute&page=%2Fapi%2Fcheck-answer%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcheck-answer%2Froute.ts&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_AI_webngonngu_infinite_language_src_app_api_check_answer_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/check-answer/route.ts */ \"(rsc)/./src/app/api/check-answer/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/check-answer/route\",\n        pathname: \"/api/check-answer\",\n        filename: \"route\",\n        bundlePath: \"app/api/check-answer/route\"\n    },\n    resolvedPagePath: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\api\\\\check-answer\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_AI_webngonngu_infinite_language_src_app_api_check_answer_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcheck-answer%2Froute&page=%2Fapi%2Fcheck-answer%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcheck-answer%2Froute.ts&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/check-answer/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/check-answer/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_questionStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/questionStore */ \"(rsc)/./src/lib/questionStore.ts\");\n\n\nasync function POST(request) {\n    console.log('POST /api/check-answer called');\n    try {\n        const body = await request.json();\n        console.log('Request body:', body);\n        const { questionId, userAnswer } = body;\n        if (!questionId || userAnswer === undefined) {\n            console.log('Missing questionId or userAnswer');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Missing questionId or userAnswer'\n            }, {\n                status: 400\n            });\n        }\n        // Get the stored question data and delete it immediately (one-time use)\n        const questionData = await _lib_questionStore__WEBPACK_IMPORTED_MODULE_1__.questionStore.getAndDelete(questionId);\n        console.log('Question data retrieved and deleted from store:', questionData);\n        if (!questionData) {\n            console.log('Question not found in store');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Question not found or expired'\n            }, {\n                status: 404\n            });\n        }\n        const isCorrect = userAnswer === questionData.correctAnswer;\n        console.log('Answer check:', {\n            userAnswer,\n            correctAnswer: questionData.correctAnswer,\n            isCorrect\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            result: {\n                isCorrect,\n                correctAnswer: questionData.correctAnswer,\n                explanation: questionData.explanation,\n                level: questionData.level,\n                language: questionData.language\n            }\n        });\n    } catch (error) {\n        console.error('Error checking answer:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to check answer'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    console.log('GET /api/check-answer called');\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Check answer API is working'\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/check-answer/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/questionStore.ts":
/*!**********************************!*\
  !*** ./src/lib/questionStore.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   questionStore: () => (/* binding */ questionStore)\n/* harmony export */ });\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redis */ \"(rsc)/./node_modules/redis/dist/index.js\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(redis__WEBPACK_IMPORTED_MODULE_0__);\n\n// Redis client configuration\nconst redis = (0,redis__WEBPACK_IMPORTED_MODULE_0__.createClient)({\n    url: process.env.REDIS_URL || 'redis://localhost:6379',\n    socket: {\n        connectTimeout: 5000\n    }\n});\n// Handle Redis connection errors\nredis.on('error', (err)=>{\n    console.error('Redis Client Error:', err);\n});\n// Connect to Redis\nlet isConnected = false;\nasync function connectRedis() {\n    if (!isConnected) {\n        try {\n            await redis.connect();\n            isConnected = true;\n            console.log('Connected to Redis');\n        } catch (error) {\n            console.error('Failed to connect to Redis:', error);\n            throw error;\n        }\n    }\n}\n// Redis-based question store with automatic cleanup\nconst questionStore = {\n    // Set question data with TTL (Time To Live)\n    async set (questionId, data) {\n        try {\n            await connectRedis();\n            const questionData = {\n                ...data,\n                createdAt: Date.now()\n            };\n            // Store with 1 hour TTL (3600 seconds)\n            await redis.setEx(`question:${questionId}`, 3600, JSON.stringify(questionData));\n        } catch (error) {\n            console.error('Error storing question in Redis:', error);\n            // Fallback to memory storage if Redis fails\n            memoryFallback.set(questionId, data);\n        }\n    },\n    // Get question data\n    async get (questionId) {\n        try {\n            await connectRedis();\n            const data = await redis.get(`question:${questionId}`);\n            if (data) {\n                return JSON.parse(data);\n            }\n            return null;\n        } catch (error) {\n            console.error('Error retrieving question from Redis:', error);\n            // Fallback to memory storage if Redis fails\n            const fallbackData = memoryFallback.get(questionId);\n            if (fallbackData) {\n                return {\n                    ...fallbackData,\n                    createdAt: Date.now() // Add current timestamp for fallback data\n                };\n            }\n            return null;\n        }\n    },\n    // Get question data and immediately delete it (one-time use)\n    async getAndDelete (questionId) {\n        try {\n            await connectRedis();\n            const data = await redis.get(`question:${questionId}`);\n            if (data) {\n                // Delete the question immediately after retrieving\n                await redis.del(`question:${questionId}`);\n                console.log('Question retrieved and deleted from Redis:', questionId);\n                return JSON.parse(data);\n            }\n            return null;\n        } catch (error) {\n            console.error('Error retrieving and deleting question from Redis:', error);\n            // Fallback to memory storage if Redis fails\n            const fallbackData = memoryFallback.get(questionId);\n            if (fallbackData) {\n                memoryFallback.delete(questionId); // Also delete from fallback\n                return {\n                    ...fallbackData,\n                    createdAt: Date.now() // Add current timestamp for fallback data\n                };\n            }\n            return null;\n        }\n    },\n    // Delete question data\n    async delete (questionId) {\n        try {\n            await connectRedis();\n            await redis.del(`question:${questionId}`);\n        } catch (error) {\n            console.error('Error deleting question from Redis:', error);\n            // Fallback to memory storage if Redis fails\n            memoryFallback.delete(questionId);\n        }\n    },\n    // Clean up expired questions (manual cleanup for additional safety)\n    async cleanup () {\n        try {\n            await connectRedis();\n            const keys = await redis.keys('question:*');\n            let deletedCount = 0;\n            for (const key of keys){\n                const data = await redis.get(key);\n                if (data) {\n                    const questionData = JSON.parse(data);\n                    const now = Date.now();\n                    const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds\n                    // Delete if older than 1 hour\n                    if (now - questionData.createdAt > oneHour) {\n                        await redis.del(key);\n                        deletedCount++;\n                    }\n                }\n            }\n            console.log(`Cleaned up ${deletedCount} expired questions`);\n            return deletedCount;\n        } catch (error) {\n            console.error('Error during cleanup:', error);\n            return 0;\n        }\n    },\n    // Get statistics\n    async getStats () {\n        try {\n            await connectRedis();\n            const keys = await redis.keys('question:*');\n            let oldestTimestamp = null;\n            for (const key of keys){\n                const data = await redis.get(key);\n                if (data) {\n                    const questionData = JSON.parse(data);\n                    if (oldestTimestamp === null || questionData.createdAt < oldestTimestamp) {\n                        oldestTimestamp = questionData.createdAt;\n                    }\n                }\n            }\n            return {\n                totalQuestions: keys.length,\n                oldestQuestion: oldestTimestamp\n            };\n        } catch (error) {\n            console.error('Error getting stats:', error);\n            return {\n                totalQuestions: 0,\n                oldestQuestion: null\n            };\n        }\n    }\n};\n// Memory fallback for when Redis is unavailable\nconst memoryFallback = new Map();\n// Periodic cleanup function (runs every 30 minutes)\nif (true) {\n    setInterval(async ()=>{\n        try {\n            await questionStore.cleanup();\n        } catch (error) {\n            console.error('Periodic cleanup failed:', error);\n        }\n    }, 30 * 60 * 1000); // 30 minutes\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/questionStore.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:timers/promises":
/*!***************************************!*\
  !*** external "node:timers/promises" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:timers/promises");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@redis","vendor-chunks/cluster-key-slot","vendor-chunks/redis"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcheck-answer%2Froute&page=%2Fapi%2Fcheck-answer%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcheck-answer%2Froute.ts&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();