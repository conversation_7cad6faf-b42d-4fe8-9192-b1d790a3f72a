"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reading/page",{

/***/ "(app-pages-browser)/./src/components/ReadingClientNew.tsx":
/*!*********************************************!*\
  !*** ./src/components/ReadingClientNew.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReadingClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AuthModal */ \"(app-pages-browser)/./src/components/AuthModal.tsx\");\n/* harmony import */ var _components_AdSense__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AdSense */ \"(app-pages-browser)/./src/components/AdSense.tsx\");\n/* harmony import */ var _components_LoadingButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/LoadingButton */ \"(app-pages-browser)/./src/components/LoadingButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ReadingClient() {\n    _s();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPassage, setSelectedPassage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedLevel, setSelectedLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('beginner');\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const [wordCount, setWordCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(300);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showVocabulary, setShowVocabulary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userAnswers, setUserAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showResults, setShowResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const passageTypes = [\n        {\n            id: 'general',\n            name: 'General',\n            description: 'General informative content'\n        },\n        {\n            id: 'news',\n            name: 'News',\n            description: 'News article style'\n        },\n        {\n            id: 'story',\n            name: 'Story',\n            description: 'Narrative stories'\n        },\n        {\n            id: 'academic',\n            name: 'Academic',\n            description: 'Academic text style'\n        },\n        {\n            id: 'science',\n            name: 'Science',\n            description: 'Science topics'\n        },\n        {\n            id: 'history',\n            name: 'History',\n            description: 'Historical content'\n        },\n        {\n            id: 'culture',\n            name: 'Culture',\n            description: 'Cultural topics'\n        }\n    ];\n    const topicSuggestions = [\n        'Technology and Innovation',\n        'Environmental Protection',\n        'Health and Wellness',\n        'Travel and Culture',\n        'Education and Learning',\n        'Food and Nutrition',\n        'Sports and Recreation',\n        'Art and Literature',\n        'Business and Economics',\n        'Science and Discovery'\n    ];\n    const levels = [\n        'beginner',\n        'intermediate',\n        'advanced'\n    ];\n    const generateReadingPassage = async ()=>{\n        if (isGenerating) return;\n        setIsGenerating(true);\n        try {\n            const response = await fetch('/api/ai/reading', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    level: selectedLevel,\n                    language: 'en',\n                    topic: selectedTopic || undefined,\n                    wordCount,\n                    passageType: selectedType\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                const passage = {\n                    id: \"ai_\".concat(Date.now()),\n                    ...data.passage\n                };\n                setSelectedPassage(passage);\n                setUserAnswers({});\n                setShowResults(false);\n                setShowVocabulary(false);\n            } else {\n                throw new Error(data.error || 'Failed to generate passage');\n            }\n        } catch (error) {\n            console.error('Error generating reading passage:', error);\n            alert(t('failedToGeneratePassage'));\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const getLevelColor = (level)=>{\n        switch(level){\n            case 'beginner':\n                return 'text-green-600 bg-green-100';\n            case 'intermediate':\n                return 'text-orange-600 bg-orange-100';\n            case 'advanced':\n                return 'text-red-600 bg-red-100';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    const handleAnswerSelect = (questionId, answerIndex)=>{\n        setUserAnswers((prev)=>({\n                ...prev,\n                [questionId]: answerIndex\n            }));\n    };\n    const calculateScore = ()=>{\n        if (!selectedPassage) return {\n            correct: 0,\n            total: 0\n        };\n        let correct = 0;\n        selectedPassage.comprehensionQuestions.forEach((question)=>{\n            if (userAnswers[question.id] === question.correctAnswer) {\n                correct++;\n            }\n        });\n        return {\n            correct,\n            total: selectedPassage.comprehensionQuestions.length\n        };\n    };\n    if (selectedPassage) {\n        const score = calculateScore();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onAuthClick: ()=>setShowAuthModal(true),\n                    showBackButton: true,\n                    onBackClick: ()=>{\n                        setSelectedPassage(null);\n                        setUserAnswers({});\n                        setShowResults(false);\n                        setShowVocabulary(false);\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-24 pb-8 px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-6 mb-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-3xl font-bold text-gray-800\",\n                                                            children: selectedPassage.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 mt-2 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCD6 \",\n                                                                        selectedPassage.readingTime\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCDD \",\n                                                                        selectedPassage.wordCount,\n                                                                        \" words\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getLevelColor(selectedPassage.level)),\n                                                                    children: selectedPassage.level\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 rounded-full text-xs\",\n                                                                    children: selectedPassage.topic\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowVocabulary(!showVocabulary),\n                                                        className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat(showVocabulary ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                                        children: \"\\uD83D\\uDCDA Vocabulary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        showVocabulary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4 p-4 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-blue-800 mb-3\",\n                                                    children: \"Key Vocabulary:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                                    children: selectedPassage.vocabulary.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white p-3 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-blue-700 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: item.word\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                            lineNumber: 203,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        item.pronunciation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"/\",\n                                                                                item.pronunciation,\n                                                                                \"/\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        item.partOfSpeech && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-blue-500 bg-blue-100 px-2 py-1 rounded\",\n                                                                            children: item.partOfSpeech\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                            lineNumber: 208,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600 mb-1\",\n                                                                    children: item.definition\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500 italic\",\n                                                                    children: [\n                                                                        '\"',\n                                                                        item.context,\n                                                                        '\"'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-8 mb-6 shadow-xl border border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none text-gray-800 leading-relaxed\",\n                                        children: selectedPassage.passage.split('\\n\\n').map((paragraph, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-4\",\n                                                children: paragraph\n                                            }, index, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-800\",\n                                                    children: \"Comprehension Questions\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-semibold text-blue-600\",\n                                                    children: [\n                                                        \"Score: \",\n                                                        score.correct,\n                                                        \"/\",\n                                                        score.total,\n                                                        \" (\",\n                                                        Math.round(score.correct / score.total * 100),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: selectedPassage.comprehensionQuestions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-800\",\n                                                                    children: [\n                                                                        index + 1,\n                                                                        \". \",\n                                                                        question.question\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(question.difficulty === 'easy' ? 'bg-green-100 text-green-600' : question.difficulty === 'medium' ? 'bg-orange-100 text-orange-600' : 'bg-red-100 text-red-600'),\n                                                                    children: question.difficulty\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2 mb-4\",\n                                                            children: question.options.map((option, optionIndex)=>{\n                                                                const isSelected = userAnswers[question.id] === optionIndex;\n                                                                const isCorrect = optionIndex === question.correctAnswer;\n                                                                const showCorrect = showResults && isCorrect;\n                                                                const showIncorrect = showResults && isSelected && !isCorrect;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>!showResults && handleAnswerSelect(question.id, optionIndex),\n                                                                    disabled: showResults,\n                                                                    className: \"w-full text-left p-3 rounded-lg border transition-colors \".concat(showCorrect ? 'bg-green-100 border-green-500 text-green-700' : showIncorrect ? 'bg-red-100 border-red-500 text-red-700' : isSelected ? 'bg-blue-100 border-blue-500 text-blue-700' : 'bg-white border-gray-300 hover:bg-gray-100', \" \").concat(showResults ? 'cursor-not-allowed' : 'cursor-pointer'),\n                                                                    children: option\n                                                                }, optionIndex, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 27\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-blue-50 text-blue-700 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"✅ Correct answer: \",\n                                                                        question.options[question.correctAnswer]\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm mt-1\",\n                                                                    children: question.explanation\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, question.id, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 text-center\",\n                                            children: !showResults ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowResults(true),\n                                                disabled: Object.keys(userAnswers).length !== selectedPassage.comprehensionQuestions.length,\n                                                className: \"px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                children: t('submitAnswers')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setUserAnswers({});\n                                                            setShowResults(false);\n                                                        },\n                                                        className: \"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\",\n                                                        children: t('tryAgain')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedPassage(null),\n                                                        className: \"px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\",\n                                                        children: t('generateNew')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedPassage.keyThemes && selectedPassage.keyThemes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 p-4 bg-purple-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-purple-800 mb-2\",\n                                                    children: \"Key Themes:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: selectedPassage.keyThemes.map((theme, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm\",\n                                                            children: theme\n                                                        }, index, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedPassage.culturalNotes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-4 bg-yellow-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-yellow-800 mb-2\",\n                                                    children: \"Cultural Notes:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-yellow-700 text-sm\",\n                                                    children: selectedPassage.culturalNotes\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdSense__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                adSlot: \"3456789012\",\n                                adFormat: \"auto\",\n                                className: \"rounded-lg\",\n                                style: {\n                                    display: 'block',\n                                    minHeight: '100px'\n                                },\n                                lazy: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isOpen: showAuthModal,\n                    onClose: ()=>setShowAuthModal(false)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onAuthClick: ()=>setShowAuthModal(true),\n                showBackButton: true,\n                onBackClick: ()=>window.history.back()\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24 pb-8 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl sm:text-5xl pb-2 font-bold bg-gradient-to-r from-gray-800 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-4\",\n                                    children: t('readingTitle')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto mb-6\",\n                                    children: t('readingSubtitle')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-8 mb-8 shadow-xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-800 mb-6\",\n                                    children: t('generateReading')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: t('level')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: selectedLevel,\n                                                    onChange: (e)=>setSelectedLevel(e.target.value),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                    children: levels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: level,\n                                                            children: level.charAt(0).toUpperCase() + level.slice(1)\n                                                        }, level, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: t('style')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: selectedType,\n                                                    onChange: (e)=>setSelectedType(e.target.value),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                    children: passageTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type.id,\n                                                            children: type.name\n                                                        }, type.id, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: t('wordCount')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: wordCount,\n                                                    onChange: (e)=>setWordCount(parseInt(e.target.value)),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 200,\n                                                            children: \"200 words\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 300,\n                                                            children: \"300 words\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 400,\n                                                            children: \"400 words\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 500,\n                                                            children: \"500 words\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                onClick: generateReadingPassage,\n                                                isLoading: isGenerating,\n                                                loadingText: t('generating'),\n                                                className: \"w-full px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-colors font-medium\",\n                                                children: [\n                                                    \"✨ \",\n                                                    t('generatePassage')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Topic (Optional - leave blank for AI to choose)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: selectedTopic,\n                                            onChange: (e)=>setSelectedTopic(e.target.value),\n                                            placeholder: \"e.g., Technology, Environment, Health...\",\n                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Or choose from popular topics:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: topicSuggestions.map((topic, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedTopic(topic),\n                                                    className: \"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm transition-colors\",\n                                                    children: topic\n                                                }, index, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                    children: \"How it works:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 font-bold\",\n                                                        children: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-800 mb-2\",\n                                                    children: \"Choose Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Select your level, style, and topic preferences\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-600 font-bold\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-800 mb-2\",\n                                                    children: t('aiGenerates')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: t('aiGeneratesDesc')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 font-bold\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-800 mb-2\",\n                                                    children: \"Practice & Learn\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Read, answer questions, and improve your skills\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                    children: \"Reading Skills Development\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Skimming and scanning techniques\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Inference and deduction skills\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-purple-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Context clue recognition\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-orange-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Main idea identification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-red-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Supporting detail analysis\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-indigo-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Author's purpose understanding\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                lineNumber: 556,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n                lineNumber: 561,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\ReadingClientNew.tsx\",\n        lineNumber: 374,\n        columnNumber: 5\n    }, this);\n}\n_s(ReadingClient, \"12sdpC8rqjsySsMWhDh4RJfjZgY=\", false, function() {\n    return [\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage\n    ];\n});\n_c = ReadingClient;\nvar _c;\n$RefreshReg$(_c, \"ReadingClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ReadingClientNew.tsx\n"));

/***/ })

});