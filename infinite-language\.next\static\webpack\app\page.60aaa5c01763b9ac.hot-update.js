"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/HistoryPage.tsx":
/*!****************************************!*\
  !*** ./src/components/HistoryPage.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HistoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction HistoryPage(param) {\n    let { onBack } = param;\n    _s();\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        level: '',\n        isCorrect: ''\n    });\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 10,\n        total: 0,\n        pages: 0\n    });\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const fetchHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HistoryPage.useCallback[fetchHistory]\": async ()=>{\n            if (!user) return;\n            setLoading(true);\n            try {\n                const params = new URLSearchParams({\n                    page: pagination.page.toString(),\n                    limit: pagination.limit.toString(),\n                    ...filters.level && {\n                        level: filters.level\n                    },\n                    ...filters.isCorrect && {\n                        isCorrect: filters.isCorrect\n                    }\n                });\n                const response = await fetch(\"/api/quiz/history?\".concat(params));\n                const data = await response.json();\n                if (data.success) {\n                    setHistory(data.data.history);\n                    setPagination(data.data.pagination);\n                }\n            } catch (error) {\n                console.error('Failed to fetch history:', error);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"HistoryPage.useCallback[fetchHistory]\"], [\n        user,\n        pagination.page,\n        pagination.limit,\n        filters\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HistoryPage.useEffect\": ()=>{\n            fetchHistory();\n        }\n    }[\"HistoryPage.useEffect\"], [\n        fetchHistory\n    ]);\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n        setPagination((prev)=>({\n                ...prev,\n                page: 1\n            }));\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString();\n    };\n    const formatTime = (seconds)=>{\n        if (seconds < 60) return \"\".concat(seconds, \"s\");\n        const minutes = Math.floor(seconds / 60);\n        const remainingSeconds = seconds % 60;\n        return \"\".concat(minutes, \"m \").concat(remainingSeconds, \"s\");\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    showBackButton: true,\n                    onBackClick: onBack\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-24 min-h-screen flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center bg-white rounded-2xl shadow-xl p-8 max-w-md mx-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-blue-500\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-800 mb-4\",\n                                children: t('loginRequired') || 'Login Required'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: t('loginToViewHistory') || 'Please login to view your quiz history'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onBack,\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200\",\n                                children: t('backToQuiz') || 'Back to Quiz'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                showBackButton: true,\n                onBackClick: onBack\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24 pb-8 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl sm:text-4xl font-bold text-gray-800 mb-2\",\n                                    children: t('quizHistory') || 'Quiz History'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Track your learning progress and review past questions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 mr-2 text-blue-500\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Filters\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: t('level') || 'Level'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filters.level,\n                                                    onChange: (e)=>handleFilterChange('level', e.target.value),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: t('allLevels') || 'All Levels'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"beginner\",\n                                                            children: t('beginner')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"intermediate\",\n                                                            children: t('intermediate')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"advanced\",\n                                                            children: t('advanced')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: t('result') || 'Result'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filters.isCorrect,\n                                                    onChange: (e)=>handleFilterChange('isCorrect', e.target.value),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: t('allResults') || 'All Results'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"true\",\n                                                            children: t('correct')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"false\",\n                                                            children: t('incorrect')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-12 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-lg\",\n                                        children: t('loading') || 'Loading...'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 15\n                            }, this) : history.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-12 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-8 h-8 text-gray-400\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-800 mb-2\",\n                                        children: t('noHistoryYet')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: t('noHistory') || 'No quiz history found'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"divide-y divide-gray-100\",\n                                children: history.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 hover:bg-gray-50 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap items-center justify-between mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 rounded-full flex items-center justify-center font-bold text-white \".concat(item.isCorrect ? 'bg-green-500' : 'bg-red-500'),\n                                                            children: item.isCorrect ? '✓' : '✗'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-3 py-1 rounded-full text-xs font-medium border \".concat(item.level === 'beginner' ? 'bg-green-50 text-green-700 border-green-200' : item.level === 'intermediate' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' : 'bg-red-50 text-red-700 border-red-200'),\n                                                                            children: t(item.level)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                \"Question #\",\n                                                                                pagination.total - (pagination.page - 1) * pagination.limit - index\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                            lineNumber: 200,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 mt-1 text-sm text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: formatDate(item.createdAt)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        item.timeSpent > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 mr-1\",\n                                                                                    fill: \"none\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                                        lineNumber: 209,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                                    lineNumber: 208,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                formatTime(item.timeSpent)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                            lineNumber: 207,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-800 text-lg leading-relaxed\",\n                                                    children: item.question\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg border-2 \".concat(item.isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-white text-sm font-bold \".concat(item.isCorrect ? 'bg-green-500' : 'bg-red-500'),\n                                                                    children: item.isCorrect ? '✓' : '✗'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: t('yourAnswer')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                            lineNumber: 238,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-base \".concat(item.isCorrect ? 'text-green-800' : 'text-red-800'),\n                                                                            children: item.options[item.userAnswer]\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                            lineNumber: 239,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    !item.isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg border-2 bg-green-50 border-green-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold\",\n                                                                    children: \"✓\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-700 mb-1\",\n                                                                            children: t('correctAnswer')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                            lineNumber: 253,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-base text-green-800\",\n                                                                            children: item.options[item.correctAnswer]\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                            lineNumber: 254,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.explanation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-blue-800 mb-1\",\n                                                                    children: t('explanation')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-700 leading-relaxed\",\n                                                                    children: item.explanation\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, item._id, true, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        pagination.pages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 bg-white rounded-xl shadow-sm border border-gray-200 p-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setPagination((prev)=>({\n                                                    ...prev,\n                                                    page: Math.max(1, prev.page - 1)\n                                                })),\n                                        disabled: pagination.page === 1,\n                                        className: \"flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 19l-7-7 7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('previous') || 'Previous'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: Array.from({\n                                            length: Math.min(5, pagination.pages)\n                                        }, (_, i)=>{\n                                            let pageNum;\n                                            if (pagination.pages <= 5) {\n                                                pageNum = i + 1;\n                                            } else if (pagination.page <= 3) {\n                                                pageNum = i + 1;\n                                            } else if (pagination.page >= pagination.pages - 2) {\n                                                pageNum = pagination.pages - 4 + i;\n                                            } else {\n                                                pageNum = pagination.page - 2 + i;\n                                            }\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setPagination((prev)=>({\n                                                            ...prev,\n                                                            page: pageNum\n                                                        })),\n                                                className: \"w-10 h-10 rounded-lg font-medium transition-colors duration-200 \".concat(pagination.page === pageNum ? 'bg-blue-500 text-white' : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'),\n                                                children: pageNum\n                                            }, pageNum, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setPagination((prev)=>({\n                                                    ...prev,\n                                                    page: Math.min(prev.pages, prev.page + 1)\n                                                })),\n                                        disabled: pagination.page === pagination.pages,\n                                        className: \"flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('next') || 'Next'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 5l7 7-7 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\HistoryPage.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n_s(HistoryPage, \"v+w9Q3ynR37k8fZkdvq/1F/JYv4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage\n    ];\n});\n_c = HistoryPage;\nvar _c;\n$RefreshReg$(_c, \"HistoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/HistoryPage.tsx\n"));

/***/ })

});