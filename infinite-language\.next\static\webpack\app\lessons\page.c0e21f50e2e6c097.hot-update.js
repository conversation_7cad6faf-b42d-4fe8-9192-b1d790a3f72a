"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/lessons/page",{

/***/ "(app-pages-browser)/./src/i18n/translations.ts":
/*!**********************************!*\
  !*** ./src/i18n/translations.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   translations: () => (/* binding */ translations)\n/* harmony export */ });\nconst translations = {\n    en: {\n        //Login\n        welcomeMessage: \"Sign in to track your progress and continue learning\",\n        // App Title\n        appTitle: \"Infinite English\",\n        appSubtitle: \"Learn English with AI-powered lessons and quizzes\",\n        // Level Selection\n        beginner: \"Beginner\",\n        intermediate: \"Intermediate\",\n        advanced: \"Advanced\",\n        beginnerDesc: \"Basic vocabulary and simple grammar\",\n        intermediateDesc: \"More complex sentences and vocabulary\",\n        advancedDesc: \"Complex grammar and advanced vocabulary\",\n        // Navigation\n        backToLevels: \"← Back to Levels\",\n        nextQuestion: \"Next Question\",\n        // Quiz\n        score: \"Score\",\n        correct: \"Correct!\",\n        incorrect: \"Incorrect!\",\n        question: \"Question:\",\n        yourAnswer: \"Your answer:\",\n        correctAnswer: \"Correct answer:\",\n        explanation: \"Explanation:\",\n        answers: \"Answers\",\n        total: \"Total\",\n        selectAnswer: \"Select your answer\",\n        answerSelected: \"Answer selected! Checking...\",\n        // Loading\n        generatingQuestion: \"Generating your question...\",\n        creatingQuestion: \"Creating a personalized question just for you\",\n        navigating: \"Navigating...\",\n        loading: \"Loading...\",\n        // Score\n        resetScore: \"Reset Score\",\n        // Language Switcher\n        language: \"Language\",\n        english: \"English\",\n        vietnamese: \"Tiếng Việt\",\n        chinese: \"中文\",\n        // Features Section\n        whyChooseTitle: \"Why Choose Infinite English?\",\n        whyChooseSubtitle: \"Experience the future of language learning with our cutting-edge features\",\n        aiGeneratedTitle: \"Unlimited Questions\",\n        aiGeneratedDesc: \"Endless unique questions with personalized learning experience tailored to your level\",\n        multiLanguageTitle: \"Multi-Language Support\",\n        multiLanguageDesc: \"Learn in Vietnamese, Chinese, or English with seamless language switching\",\n        progressTrackingTitle: \"Progress Tracking\",\n        progressTrackingDesc: \"Monitor your learning journey with detailed statistics and performance analytics\",\n        // Stats\n        questionsLabel: \"Questions\",\n        languagesLabel: \"Languages\",\n        aiPoweredLabel: \"Smart Learning\",\n        // Authentication\n        login: \"Login\",\n        register: \"Register\",\n        logout: \"Logout\",\n        email: \"Email\",\n        username: \"Username\",\n        password: \"Password\",\n        confirmPassword: \"Confirm Password\",\n        needAccount: \"Don't have an account? Register\",\n        haveAccount: \"Already have an account? Login\",\n        loginRequired: \"Login Required\",\n        loginToViewHistory: \"Please login to view your quiz history\",\n        // User Stats\n        stats: \"Statistics\",\n        totalQuestions: \"Total Questions\",\n        accuracy: \"Accuracy\",\n        streak: \"Streak\",\n        viewHistory: \"View History\",\n        // History\n        quizHistory: \"Quiz History\",\n        backToQuiz: \"Back to Quiz\",\n        allLevels: \"All Levels\",\n        allResults: \"All Results\",\n        result: \"Result\",\n        noHistory: \"No quiz history found\",\n        previous: \"Previous\",\n        next: \"Next\",\n        // AI Features\n        aiLessons: \"AI Lessons\",\n        aiReading: \"AI Reading\",\n        aiTutor: \"AI Tutor\",\n        createLesson: \"Create Lesson\",\n        generateReading: \"Generate Reading\",\n        askTutor: \"Ask AI Tutor\",\n        // Lessons\n        lessonTitle: \"AI English Lessons\",\n        lessonSubtitle: \"Create personalized English lessons with AI. Choose your topic, level, and lesson type to generate comprehensive learning content.\",\n        lessonType: \"Lesson Type\",\n        duration: \"Duration\",\n        topic: \"Topic\",\n        topicPlaceholder: \"e.g., Present Simple Tense, Business English, Travel Vocabulary...\",\n        topicSuggestions: \"Or choose from popular topics:\",\n        generateLesson: \"Generate Lesson\",\n        generating: \"Generating...\",\n        // Reading\n        readingTitle: \"AI Reading Practice\",\n        readingSubtitle: \"Generate personalized reading passages with AI. Choose your level, topic, and style to create unlimited practice content.\",\n        wordCount: \"Word Count\",\n        style: \"Style\",\n        generatePassage: \"Generate Passage\",\n        vocabulary: \"Vocabulary\",\n        comprehensionQuestions: \"Comprehension Questions\",\n        submitAnswers: \"Submit Answers\",\n        tryAgain: \"Try Again\",\n        generateNew: \"Generate New Passage\",\n        // Dashboard\n        dashboardTitle: \"Learning Dashboard\",\n        dashboardSubtitle: \"Track your progress, analyze your performance, and get personalized recommendations to improve your English.\",\n        quizzesTaken: \"Quizzes Taken\",\n        overallPerformance: \"Overall Performance\",\n        streakDays: \"Day Streak\",\n        studyTime: \"Study Time\",\n        progressByLevel: \"Progress by Level\",\n        aiAnalysis: \"AI Performance Analysis\",\n        strengths: \"Strengths\",\n        improvements: \"Areas for Improvement\",\n        nextSteps: \"Next Steps\",\n        motivationalMessage: \"Motivational Message\",\n        continuelearning: \"Continue Learning\",\n        // Tutor\n        tutorTitle: \"AI English Tutor\",\n        tutorSubtitle: \"Get instant help with grammar, vocabulary, pronunciation, and more. Ask questions in your native language.\",\n        askQuestion: \"Ask a question...\",\n        send: \"Send\",\n        grammarHelp: \"Grammar Help\",\n        vocabularyHelp: \"Vocabulary\",\n        pronunciationHelp: \"Pronunciation\",\n        writingHelp: \"Writing Help\",\n        conversationHelp: \"Conversation\",\n        cultureHelp: \"Culture\",\n        // Footer\n        footerDescription: \"Learn English for free with our AI-powered platform designed for Vietnamese learners. Unlimited multiple choice questions, structured lessons, vocabulary and effective learning tips. Suitable for all levels from basic to advanced.\",\n        contactAdvertising: \"Contact for Advertising\",\n        quickLinks: \"Quick Links\",\n        about: \"About\",\n        contact: \"Contact\",\n        legal: \"Legal\",\n        terms: \"Terms of Service\",\n        privacy: \"Privacy Policy\",\n        allRightsReserved: \"All rights reserved.\",\n        disclaimer: \"Disclaimer\",\n        disclaimerText: \"Our website only provides online English learning services for entertainment and content sharing purposes. All learning content posted on the website is collected from various sources on the internet and we are not responsible for copyright or ownership of any content. If you are a copyright owner and believe that content on the site violates your rights, please contact us to remove the infringing content promptly. In addition, we are not responsible for advertising content displayed on the website, including but not limited to advertising products or services of third parties. These advertisements do not reflect our views or commitments. Users need to consider and take responsibility when interacting with such advertisements.\",\n        // Common\n        level: \"Level\",\n        back: \"Back\",\n        continue: \"Continue\",\n        complete: \"Complete\",\n        start: \"Start\",\n        finish: \"Finish\",\n        save: \"Save\",\n        cancel: \"Cancel\",\n        close: \"Close\",\n        signIn: \"Sign In\",\n        signOut: \"Sign Out\",\n        signUp: \"Sign Up\",\n        // Auth Modal\n        continueWithGoogle: \"Continue with Google\",\n        continueWithFacebook: \"Continue with Facebook\",\n        optionalLoginNote: \"Login is optional. You can continue without an account, but your progress won't be saved.\",\n        loginError: \"Failed to login with {provider}. Please try again.\",\n        // Language Switcher\n        active: \"Active\",\n        // User Menu\n        learningDashboard: \"Learning Dashboard\",\n        accuracyText: \"accuracy\",\n        // Error Messages\n        aiErrorMessage: \"Sorry, I encountered an error while processing your question. Please try again.\",\n        failedToGenerate: \"Failed to Generate Question\",\n        questionGenerateError: \"Sorry, we couldn't generate a question at this time. Please try again later.\",\n        tryAgainLater: \"Try Again Later\",\n        backToHome: \"Back to Home\",\n        // Loading States\n        processing: \"Processing...\",\n        pleaseWait: \"Please wait...\",\n        loadingHistory: \"Loading your quiz history...\",\n        // General Error States\n        somethingWentWrong: \"Something went wrong\",\n        errorOccurred: \"An error occurred\",\n        retryAction: \"Retry\",\n        // Additional Reading Component\n        aiGenerates: \"AI Generates\",\n        aiGeneratesDesc: \"Our AI creates a unique passage with questions\",\n        failedToGeneratePassage: \"Failed to generate reading passage. Please try again.\",\n        // Additional Lessons Component\n        aiCreates: \"AI Creates\",\n        aiCreatesDesc: \"AI generates comprehensive lesson content\",\n        failedToGenerateLesson: \"Failed to generate lesson. Please try again.\",\n        // History Page\n        noHistoryYet: \"No History Yet\",\n        // Question Card\n        processingAnswer: \"Processing your answer...\",\n        chooseAnswer: \"Choose your answer\"\n    },\n    vi: {\n        //Login\n        welcomeMessage: \"Đăng nhập để theo dõi tiến độ và tiếp tục học\",\n        // App Title\n        appTitle: \"Infinite English\",\n        appSubtitle: \"Học tiếng Anh với bài học và quiz AI\",\n        // Level Selection\n        beginner: \"Cơ bản\",\n        intermediate: \"Trung bình\",\n        advanced: \"Nâng cao\",\n        beginnerDesc: \"Từ vựng cơ bản và ngữ pháp đơn giản\",\n        intermediateDesc: \"Câu và từ vựng phức tạp hơn\",\n        advancedDesc: \"Ngữ pháp phức tạp và từ vựng nâng cao\",\n        // Navigation\n        backToLevels: \"Quay lại Cấp độ\",\n        nextQuestion: \"Câu hỏi tiếp theo\",\n        // Quiz\n        score: \"Điểm\",\n        correct: \"Đúng rồi!\",\n        incorrect: \"Sai rồi!\",\n        question: \"Câu hỏi:\",\n        yourAnswer: \"Câu trả lời của bạn:\",\n        correctAnswer: \"Đáp án đúng:\",\n        explanation: \"Giải thích:\",\n        answers: \"Câu trả lời\",\n        total: \"Tổng\",\n        selectAnswer: \"Chọn câu trả lời của bạn\",\n        answerSelected: \"Đã chọn câu trả lời! Đang kiểm tra...\",\n        // Loading\n        generatingQuestion: \"Đang tạo câu hỏi cho bạn...\",\n        creatingQuestion: \"Đang tạo câu hỏi cá nhân hóa dành riêng cho bạn\",\n        navigating: \"Đang chuyển trang...\",\n        loading: \"Đang tải...\",\n        // Score\n        resetScore: \"Đặt lại điểm\",\n        // Language Switcher\n        language: \"Ngôn ngữ\",\n        english: \"English\",\n        vietnamese: \"Tiếng Việt\",\n        chinese: \"中文\",\n        // Features Section\n        whyChooseTitle: \"Tại sao chọn Infinite English?\",\n        whyChooseSubtitle: \"Trải nghiệm tương lai của việc học ngôn ngữ với các tính năng tiên tiến\",\n        aiGeneratedTitle: \"Câu hỏi vô hạn\",\n        aiGeneratedDesc: \"Câu hỏi độc đáo không giới hạn với trải nghiệm học tập cá nhân hóa phù hợp với trình độ của bạn\",\n        multiLanguageTitle: \"Hỗ trợ đa ngôn ngữ\",\n        multiLanguageDesc: \"Học bằng tiếng Việt, tiếng Trung hoặc tiếng Anh với khả năng chuyển đổi ngôn ngữ liền mạch\",\n        progressTrackingTitle: \"Theo dõi tiến độ\",\n        progressTrackingDesc: \"Theo dõi hành trình học tập của bạn với thống kê chi tiết và phân tích hiệu suất\",\n        // Stats\n        questionsLabel: \"Câu hỏi\",\n        languagesLabel: \"Ngôn ngữ\",\n        aiPoweredLabel: \"Học thông minh\",\n        // Authentication\n        login: \"Đăng nhập\",\n        register: \"Đăng ký\",\n        logout: \"Đăng xuất\",\n        email: \"Email\",\n        username: \"Tên người dùng\",\n        password: \"Mật khẩu\",\n        confirmPassword: \"Xác nhận mật khẩu\",\n        needAccount: \"Chưa có tài khoản? Đăng ký\",\n        haveAccount: \"Đã có tài khoản? Đăng nhập\",\n        loginRequired: \"Yêu cầu đăng nhập\",\n        loginToViewHistory: \"Vui lòng đăng nhập để xem lịch sử quiz\",\n        // User Stats\n        stats: \"Thống kê\",\n        totalQuestions: \"Tổng câu hỏi\",\n        accuracy: \"Độ chính xác\",\n        streak: \"Chuỗi đúng\",\n        viewHistory: \"Xem lịch sử\",\n        // History\n        quizHistory: \"Lịch sử Quiz\",\n        backToQuiz: \"Quay lại Quiz\",\n        allLevels: \"Tất cả cấp độ\",\n        allResults: \"Tất cả kết quả\",\n        result: \"Kết quả\",\n        noHistory: \"Không tìm thấy lịch sử quiz\",\n        previous: \"Trước\",\n        next: \"Tiếp\",\n        // AI Features\n        aiLessons: \"Bài học AI\",\n        aiReading: \"Đọc hiểu AI\",\n        aiTutor: \"Gia sư AI\",\n        createLesson: \"Tạo bài học\",\n        generateReading: \"Tạo bài đọc\",\n        askTutor: \"Hỏi gia sư AI\",\n        // Lessons\n        lessonTitle: \"Bài học tiếng Anh AI\",\n        lessonSubtitle: \"Tạo bài học tiếng Anh cá nhân hóa với AI. Chọn chủ đề, cấp độ và loại bài học để tạo nội dung học tập toàn diện.\",\n        lessonType: \"Loại bài học\",\n        duration: \"Thời lượng\",\n        topic: \"Chủ đề\",\n        topicPlaceholder: \"ví dụ: Thì hiện tại đơn, Tiếng Anh thương mại, Từ vựng du lịch...\",\n        topicSuggestions: \"Hoặc chọn từ các chủ đề phổ biến:\",\n        generateLesson: \"Tạo bài học\",\n        generating: \"Đang tạo...\",\n        // Reading\n        readingTitle: \"Luyện đọc hiểu AI\",\n        readingSubtitle: \"Tạo đoạn văn đọc hiểu cá nhân hóa với AI. Chọn cấp độ, chủ đề và phong cách để tạo nội dung luyện tập không giới hạn.\",\n        wordCount: \"Số từ\",\n        style: \"Phong cách\",\n        generatePassage: \"Tạo đoạn văn\",\n        vocabulary: \"Từ vựng\",\n        comprehensionQuestions: \"Câu hỏi đọc hiểu\",\n        submitAnswers: \"Nộp bài\",\n        tryAgain: \"Thử lại\",\n        generateNew: \"Tạo đoạn văn mới\",\n        // Dashboard\n        dashboardTitle: \"Bảng điều khiển học tập\",\n        dashboardSubtitle: \"Theo dõi tiến độ, phân tích hiệu suất và nhận gợi ý cá nhân hóa để cải thiện tiếng Anh.\",\n        quizzesTaken: \"Bài quiz đã làm\",\n        overallPerformance: \"Hiệu suất tổng thể\",\n        streakDays: \"Chuỗi ngày\",\n        studyTime: \"Thời gian học\",\n        progressByLevel: \"Tiến độ theo cấp độ\",\n        aiAnalysis: \"Phân tích hiệu suất AI\",\n        strengths: \"Điểm mạnh\",\n        improvements: \"Cần cải thiện\",\n        nextSteps: \"Bước tiếp theo\",\n        motivationalMessage: \"Thông điệp động viên\",\n        continuelearning: \"Tiếp tục học\",\n        // Tutor\n        tutorTitle: \"Gia sư tiếng Anh AI\",\n        tutorSubtitle: \"Nhận trợ giúp tức thì về ngữ pháp, từ vựng, phát âm và nhiều hơn nữa. Đặt câu hỏi bằng tiếng mẹ đẻ.\",\n        askQuestion: \"Đặt câu hỏi...\",\n        send: \"Gửi\",\n        grammarHelp: \"Trợ giúp ngữ pháp\",\n        vocabularyHelp: \"Từ vựng\",\n        pronunciationHelp: \"Phát âm\",\n        writingHelp: \"Trợ giúp viết\",\n        conversationHelp: \"Hội thoại\",\n        cultureHelp: \"Văn hóa\",\n        // Footer\n        footerDescription: \"Học tiếng Anh miễn phí với nền tảng AI thông minh dành cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả. Phù hợp mọi trình độ từ cơ bản đến nâng cao.\",\n        contactAdvertising: \"Liên hệ đặt quảng cáo\",\n        quickLinks: \"Liên kết nhanh\",\n        about: \"Giới thiệu\",\n        contact: \"Liên hệ\",\n        legal: \"Pháp lý\",\n        terms: \"Điều khoản\",\n        privacy: \"Chính sách bảo mật\",\n        allRightsReserved: \"Tất cả quyền được bảo lưu.\",\n        disclaimer: \"Miễn trừ trách nhiệm\",\n        disclaimerText: \"Trang web của chúng tôi chỉ cung cấp dịch vụ học tiếng Anh online với mục đích giải trí và chia sẻ nội dung. Toàn bộ nội dung học tập được đăng tải trên trang web được sưu tầm từ nhiều nguồn trên internet và chúng tôi không chịu trách nhiệm về bản quyền hoặc quyền sở hữu đối với bất kỳ nội dung nào. Nếu bạn là chủ sở hữu bản quyền và cho rằng nội dung trên trang vi phạm quyền của bạn, vui lòng liên hệ với chúng tôi để tiến hành gỡ bỏ nội dung vi phạm một cách kịp thời. Ngoài ra, chúng tôi không chịu trách nhiệm về các nội dung quảng cáo hiển thị trên trang web, bao gồm nhưng không giới hạn ở việc quảng cáo sản phẩm hoặc dịch vụ của bên thứ ba. Những quảng cáo này không phản ánh quan điểm hoặc cam kết của chúng tôi. Người dùng cần tự cân nhắc và chịu trách nhiệm khi tương tác với các quảng cáo đó.\",\n        // Common\n        level: \"Cấp độ\",\n        back: \"Quay lại\",\n        continue: \"Tiếp tục\",\n        complete: \"Hoàn thành\",\n        start: \"Bắt đầu\",\n        finish: \"Kết thúc\",\n        save: \"Lưu\",\n        cancel: \"Hủy\",\n        close: \"Đóng\",\n        signIn: \"Đăng nhập\",\n        signOut: \"Đăng xuất\",\n        signUp: \"Đăng ký\",\n        // Auth Modal\n        continueWithGoogle: \"Tiếp tục với Google\",\n        continueWithFacebook: \"Tiếp tục với Facebook\",\n        optionalLoginNote: \"Đăng nhập là tùy chọn. Bạn có thể tiếp tục mà không cần tài khoản, nhưng tiến độ sẽ không được lưu.\",\n        loginError: \"Đăng nhập với {provider} thất bại. Vui lòng thử lại.\",\n        // Language Switcher\n        active: \"Đang hoạt động\",\n        // User Menu\n        learningDashboard: \"Bảng điều khiển học tập\",\n        accuracyText: \"độ chính xác\",\n        // Error Messages\n        aiErrorMessage: \"Xin lỗi, tôi gặp lỗi khi xử lý câu hỏi của bạn. Vui lòng thử lại.\",\n        failedToGenerate: \"Không thể tạo câu hỏi\",\n        questionGenerateError: \"Xin lỗi, chúng tôi không thể tạo câu hỏi vào lúc này. Vui lòng thử lại sau.\",\n        tryAgainLater: \"Thử lại sau\",\n        backToHome: \"Về trang chủ\",\n        // Loading States\n        processing: \"Đang xử lý...\",\n        pleaseWait: \"Vui lòng đợi...\",\n        loadingHistory: \"Đang tải lịch sử quiz của bạn...\",\n        // General Error States\n        somethingWentWrong: \"Đã xảy ra lỗi\",\n        errorOccurred: \"Đã xảy ra lỗi\",\n        retryAction: \"Thử lại\",\n        // Additional Reading Component\n        aiGenerates: \"AI Tạo nội dung\",\n        aiGeneratesDesc: \"AI của chúng tôi tạo ra đoạn văn độc đáo với các câu hỏi\",\n        failedToGeneratePassage: \"Không thể tạo đoạn văn đọc hiểu. Vui lòng thử lại.\",\n        // Additional Lessons Component\n        aiCreates: \"AI Tạo bài học\",\n        aiCreatesDesc: \"AI tạo ra nội dung bài học toàn diện\",\n        failedToGenerateLesson: \"Không thể tạo bài học. Vui lòng thử lại.\",\n        // History Page\n        noHistoryYet: \"Chưa có lịch sử\",\n        // Question Card\n        processingAnswer: \"Đang xử lý câu trả lời của bạn...\",\n        chooseAnswer: \"Chọn câu trả lời của bạn\"\n    },\n    zh: {\n        //Login\n        welcomeMessage: \"登录以跟踪您的进度并继续学习\",\n        // App Title\n        appTitle: \"Infinite English\",\n        appSubtitle: \"用AI课程和测验学英语\",\n        // Level Selection\n        beginner: \"初级\",\n        intermediate: \"中级\",\n        advanced: \"高级\",\n        beginnerDesc: \"基础词汇和简单语法\",\n        intermediateDesc: \"更复杂的句子和词汇\",\n        advancedDesc: \"复杂语法和高级词汇\",\n        // Navigation\n        backToLevels: \"← 返回级别\",\n        nextQuestion: \"下一题\",\n        // Quiz\n        score: \"分数\",\n        correct: \"正确！\",\n        incorrect: \"错误！\",\n        question: \"问题：\",\n        yourAnswer: \"您的答案：\",\n        correctAnswer: \"正确答案：\",\n        explanation: \"解释：\",\n        answers: \"答案\",\n        total: \"总计\",\n        selectAnswer: \"选择您的答案\",\n        answerSelected: \"已选择答案！正在检查...\",\n        // Loading\n        generatingQuestion: \"正在为您生成问题...\",\n        creatingQuestion: \"正在为您创建个性化问题\",\n        navigating: \"正在导航...\",\n        loading: \"加载中...\",\n        // Score\n        resetScore: \"重置分数\",\n        // Language Switcher\n        language: \"语言\",\n        english: \"English\",\n        vietnamese: \"Tiếng Việt\",\n        chinese: \"中文\",\n        // Features Section\n        whyChooseTitle: \"为什么选择 Infinite English？\",\n        whyChooseSubtitle: \"体验我们尖端功能带来的语言学习未来\",\n        aiGeneratedTitle: \"无限题库\",\n        aiGeneratedDesc: \"无限独特问题，提供适合您水平的个性化学习体验\",\n        multiLanguageTitle: \"多语言支持\",\n        multiLanguageDesc: \"支持越南语、中文或英语学习，语言切换无缝衔接\",\n        progressTrackingTitle: \"进度跟踪\",\n        progressTrackingDesc: \"通过详细统计和性能分析监控您的学习历程\",\n        // Stats\n        questionsLabel: \"问题\",\n        languagesLabel: \"语言\",\n        aiPoweredLabel: \"智能学习\",\n        // Authentication\n        login: \"登录\",\n        register: \"注册\",\n        logout: \"退出\",\n        email: \"邮箱\",\n        username: \"用户名\",\n        password: \"密码\",\n        confirmPassword: \"确认密码\",\n        needAccount: \"没有账户？注册\",\n        haveAccount: \"已有账户？登录\",\n        loginRequired: \"需要登录\",\n        loginToViewHistory: \"请登录查看测验历史\",\n        // User Stats\n        stats: \"统计\",\n        totalQuestions: \"总题数\",\n        accuracy: \"准确率\",\n        streak: \"连续正确\",\n        viewHistory: \"查看历史\",\n        // History\n        quizHistory: \"测验历史\",\n        backToQuiz: \"返回测验\",\n        allLevels: \"所有级别\",\n        allResults: \"所有结果\",\n        result: \"结果\",\n        noHistory: \"未找到测验历史\",\n        previous: \"上一页\",\n        next: \"下一页\",\n        // AI Features\n        aiLessons: \"AI课程\",\n        aiReading: \"AI阅读\",\n        aiTutor: \"AI导师\",\n        createLesson: \"创建课程\",\n        generateReading: \"生成阅读\",\n        askTutor: \"询问AI导师\",\n        // Lessons\n        lessonTitle: \"AI英语课程\",\n        lessonSubtitle: \"使用AI创建个性化英语课程。选择您的主题、级别和课程类型来生成全面的学习内容。\",\n        lessonType: \"课程类型\",\n        duration: \"时长\",\n        topic: \"主题\",\n        topicPlaceholder: \"例如：一般现在时、商务英语、旅游词汇...\",\n        topicSuggestions: \"或从热门主题中选择：\",\n        generateLesson: \"生成课程\",\n        generating: \"生成中...\",\n        // Reading\n        readingTitle: \"AI阅读练习\",\n        readingSubtitle: \"使用AI生成个性化阅读文章。选择您的级别、主题和风格来创建无限练习内容。\",\n        wordCount: \"字数\",\n        style: \"风格\",\n        generatePassage: \"生成文章\",\n        vocabulary: \"词汇\",\n        comprehensionQuestions: \"理解问题\",\n        submitAnswers: \"提交答案\",\n        tryAgain: \"重试\",\n        generateNew: \"生成新文章\",\n        // Dashboard\n        dashboardTitle: \"学习仪表板\",\n        dashboardSubtitle: \"跟踪您的进度，分析您的表现，并获得个性化建议来提高您的英语。\",\n        quizzesTaken: \"已完成测验\",\n        overallPerformance: \"整体表现\",\n        streakDays: \"连续天数\",\n        studyTime: \"学习时间\",\n        progressByLevel: \"按级别进度\",\n        aiAnalysis: \"AI表现分析\",\n        strengths: \"优势\",\n        improvements: \"需要改进\",\n        nextSteps: \"下一步\",\n        motivationalMessage: \"激励信息\",\n        continuelearning: \"继续学习\",\n        // Tutor\n        tutorTitle: \"AI英语导师\",\n        tutorSubtitle: \"获得语法、词汇、发音等方面的即时帮助。用您的母语提问。\",\n        askQuestion: \"提问...\",\n        send: \"发送\",\n        grammarHelp: \"语法帮助\",\n        vocabularyHelp: \"词汇\",\n        pronunciationHelp: \"发音\",\n        writingHelp: \"写作帮助\",\n        conversationHelp: \"对话\",\n        cultureHelp: \"文化\",\n        // Footer\n        footerDescription: \"使用我们专为越南学习者设计的AI平台免费学习英语。无限选择题、结构化课程、词汇和有效的学习技巧。适合从基础到高级的所有水平。\",\n        contactAdvertising: \"广告联系\",\n        quickLinks: \"快速链接\",\n        about: \"关于我们\",\n        contact: \"联系我们\",\n        legal: \"法律\",\n        terms: \"服务条款\",\n        privacy: \"隐私政策\",\n        allRightsReserved: \"版权所有。\",\n        disclaimer: \"免责声明\",\n        disclaimerText: \"我们的网站仅提供在线英语学习服务，用于娱乐和内容分享目的。网站上发布的所有学习内容均来自互联网上的各种来源，我们对任何内容的版权或所有权不承担责任。如果您是版权所有者并认为网站上的内容侵犯了您的权利，请联系我们及时删除侵权内容。此外，我们对网站上显示的广告内容不承担责任，包括但不限于第三方产品或服务的广告。这些广告不代表我们的观点或承诺。用户在与此类广告互动时需要自行考虑并承担责任。\",\n        // Common\n        level: \"级别\",\n        back: \"返回\",\n        continue: \"继续\",\n        complete: \"完成\",\n        start: \"开始\",\n        finish: \"结束\",\n        save: \"保存\",\n        cancel: \"取消\",\n        close: \"关闭\",\n        signIn: \"登录\",\n        signOut: \"退出\",\n        signUp: \"注册\",\n        // Auth Modal\n        continueWithGoogle: \"使用Google继续\",\n        continueWithFacebook: \"使用Facebook继续\",\n        optionalLoginNote: \"登录是可选的。您可以在没有账户的情况下继续，但您的进度不会被保存。\",\n        loginError: \"使用{provider}登录失败。请重试。\",\n        // Language Switcher\n        active: \"活跃\",\n        // User Menu\n        learningDashboard: \"学习仪表板\",\n        accuracyText: \"准确率\",\n        // Error Messages\n        aiErrorMessage: \"抱歉，处理您的问题时遇到错误。请重试。\",\n        failedToGenerate: \"生成问题失败\",\n        questionGenerateError: \"抱歉，我们目前无法生成问题。请稍后重试。\",\n        tryAgainLater: \"稍后重试\",\n        backToHome: \"返回首页\",\n        // Loading States\n        processing: \"处理中...\",\n        pleaseWait: \"请稍候...\",\n        loadingHistory: \"正在加载您的测验历史...\",\n        // General Error States\n        somethingWentWrong: \"出现问题\",\n        errorOccurred: \"发生错误\",\n        retryAction: \"重试\",\n        // Additional Reading Component\n        aiGenerates: \"AI生成\",\n        aiGeneratesDesc: \"我们的AI创建独特的文章和问题\",\n        failedToGeneratePassage: \"生成阅读文章失败。请重试。\",\n        // Additional Lessons Component\n        aiCreates: \"AI创建\",\n        aiCreatesDesc: \"AI生成全面的课程内容\",\n        failedToGenerateLesson: \"生成课程失败。请重试。\",\n        // History Page\n        noHistoryYet: \"暂无历史记录\",\n        // Question Card\n        processingAnswer: \"正在处理您的答案...\",\n        chooseAnswer: \"选择您的答案\"\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/i18n/translations.ts\n"));

/***/ })

});