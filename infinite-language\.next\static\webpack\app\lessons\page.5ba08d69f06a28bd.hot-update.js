"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/lessons/page",{

/***/ "(app-pages-browser)/./src/components/LessonsClient.tsx":
/*!******************************************!*\
  !*** ./src/components/LessonsClient.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LessonsClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(app-pages-browser)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_AuthModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AuthModal */ \"(app-pages-browser)/./src/components/AuthModal.tsx\");\n/* harmony import */ var _components_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MarkdownRenderer */ \"(app-pages-browser)/./src/components/MarkdownRenderer.tsx\");\n/* harmony import */ var _components_AdSense__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/AdSense */ \"(app-pages-browser)/./src/components/AdSense.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LessonsClient() {\n    _s();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedLesson, setSelectedLesson] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentSectionIndex, setCurrentSectionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedLevel, setSelectedLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('beginner');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grammar');\n    const [topic, setTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(30);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userAnswers, setUserAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showVocabulary, setShowVocabulary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const lessonTypes = [\n        {\n            id: 'grammar',\n            name: 'Grammar',\n            icon: '📝',\n            description: 'Grammar rules and structures'\n        },\n        {\n            id: 'vocabulary',\n            name: 'Vocabulary',\n            icon: '📚',\n            description: 'Vocabulary building and word usage'\n        },\n        {\n            id: 'conversation',\n            name: 'Conversation',\n            icon: '💬',\n            description: 'Conversational skills and dialogue practice'\n        },\n        {\n            id: 'pronunciation',\n            name: 'Pronunciation',\n            icon: '🗣️',\n            description: 'Pronunciation and phonetics'\n        },\n        {\n            id: 'writing',\n            name: 'Writing',\n            icon: '✍️',\n            description: 'Writing skills and composition'\n        },\n        {\n            id: 'listening',\n            name: 'Listening',\n            icon: '👂',\n            description: 'Listening comprehension and audio skills'\n        },\n        {\n            id: 'culture',\n            name: 'Culture',\n            icon: '🌍',\n            description: 'Cultural understanding and context'\n        }\n    ];\n    const topicSuggestions = [\n        'Present Simple Tense',\n        'Past Tense Forms',\n        'Future Tense',\n        'Conditional Sentences',\n        'Modal Verbs',\n        'Phrasal Verbs',\n        'Business English',\n        'Travel Vocabulary',\n        'Food and Cooking',\n        'Technology Terms',\n        'Daily Routines',\n        'Family and Relationships'\n    ];\n    const levels = [\n        'beginner',\n        'intermediate',\n        'advanced'\n    ];\n    const generateLesson = async ()=>{\n        if (isGenerating || !topic.trim()) return;\n        setIsGenerating(true);\n        try {\n            const response = await fetch('/api/ai/lesson', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    level: selectedLevel,\n                    language: 'en',\n                    topic: topic.trim(),\n                    lessonType: selectedType,\n                    duration\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                const lesson = {\n                    id: \"ai_\".concat(Date.now()),\n                    ...data.lesson\n                };\n                setSelectedLesson(lesson);\n                setCurrentSectionIndex(0);\n                setUserAnswers({});\n                setShowVocabulary(false);\n            } else {\n                throw new Error(data.error || 'Failed to generate lesson');\n            }\n        } catch (error) {\n            console.error('Error generating lesson:', error);\n            alert(t('failedToGenerateLesson'));\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const getLevelColor = (level)=>{\n        switch(level){\n            case 'beginner':\n                return 'text-secondary-30 bg-secondary-30/10';\n            case 'intermediate':\n                return 'text-highlight-dark bg-highlight/10';\n            case 'advanced':\n                return 'text-accent-dark bg-accent-10/10';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    const handleExerciseAnswer = (exerciseId, answer)=>{\n        setUserAnswers((prev)=>({\n                ...prev,\n                [exerciseId]: answer\n            }));\n    };\n    const nextSection = ()=>{\n        if (selectedLesson && currentSectionIndex < selectedLesson.sections.length - 1) {\n            setCurrentSectionIndex(currentSectionIndex + 1);\n        }\n    };\n    const prevSection = ()=>{\n        if (currentSectionIndex > 0) {\n            setCurrentSectionIndex(currentSectionIndex - 1);\n        }\n    };\n    if (selectedLesson) {\n        var _currentSection_exercises;\n        const currentSection = selectedLesson.sections[currentSectionIndex];\n        const progress = (currentSectionIndex + 1) / selectedLesson.sections.length * 100;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onAuthClick: ()=>setShowAuthModal(true),\n                    showBackButton: true,\n                    onBackClick: ()=>{\n                        setSelectedLesson(null);\n                        setCurrentSectionIndex(0);\n                        setUserAnswers({});\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-24 pb-8 px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-6 mb-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-3xl font-bold text-gray-800\",\n                                                            children: selectedLesson.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mt-1\",\n                                                            children: selectedLesson.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 mt-2 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"⏱️ \",\n                                                                        selectedLesson.duration,\n                                                                        \" min\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getLevelColor(selectedLesson.level)),\n                                                                    children: selectedLesson.level\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 rounded-full text-xs\",\n                                                                    children: selectedLesson.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowVocabulary(!showVocabulary),\n                                                    className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat(showVocabulary ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                                    children: \"\\uD83D\\uDCDA Vocabulary\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-sm text-gray-600 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Progress\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                currentSectionIndex + 1,\n                                                                \" of \",\n                                                                selectedLesson.sections.length\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(progress, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        showVocabulary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4 p-4 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-blue-800 mb-3\",\n                                                    children: \"Lesson Vocabulary:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                                    children: selectedLesson.vocabulary.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white p-3 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-blue-700 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: item.word\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                            lineNumber: 228,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        item.pronunciation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"/\",\n                                                                                item.pronunciation,\n                                                                                \"/\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                            lineNumber: 230,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600 mb-1\",\n                                                                    children: item.definition\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500 italic\",\n                                                                    children: [\n                                                                        '\"',\n                                                                        item.example,\n                                                                        '\"'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-8 mb-6 shadow-xl border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-gray-800\",\n                                                    children: currentSection.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"⏱️ \",\n                                                        currentSection.duration,\n                                                        \" min\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        currentSection.type === 'explanation' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-700 leading-relaxed mb-6\",\n                                                    children: currentSection.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        content: currentSection.content,\n                                                        className: \"text-gray-700 leading-relaxed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this),\n                                                currentSection.examples && currentSection.examples.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                                            children: \"Examples:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: currentSection.examples.map((example, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        content: example,\n                                                                        className: \"text-gray-700\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, index, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this),\n                                        currentSection.type === 'exercise' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: (_currentSection_exercises = currentSection.exercises) === null || _currentSection_exercises === void 0 ? void 0 : _currentSection_exercises.map((exercise, index)=>{\n                                                var _exercise_options, _exercise_answer, _exercise_answer1, _exercise_answer2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-800 mb-4\",\n                                                            children: [\n                                                                \"Exercise \",\n                                                                index + 1,\n                                                                \":\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    content: exercise.question,\n                                                                    className: \"inline ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        exercise.type === 'multiple-choice' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: (_exercise_options = exercise.options) === null || _exercise_options === void 0 ? void 0 : _exercise_options.map((option, optionIndex)=>{\n                                                                const isSelected = userAnswers[exercise.id] === optionIndex;\n                                                                const isCorrect = optionIndex === exercise.correctAnswer;\n                                                                const showResult = userAnswers[exercise.id] !== undefined;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleExerciseAnswer(exercise.id, optionIndex),\n                                                                    className: \"w-full text-left p-3 rounded-lg border transition-colors \".concat(showResult && isCorrect ? 'bg-green-100 border-green-500 text-green-700' : showResult && isSelected && !isCorrect ? 'bg-red-100 border-red-500 text-red-700' : isSelected ? 'bg-blue-100 border-blue-500 text-blue-700' : 'bg-white border-gray-300 hover:bg-gray-100'),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        content: option,\n                                                                        className: \"text-inherit\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, optionIndex, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 31\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        exercise.type === 'true-false' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                'True',\n                                                                'False'\n                                                            ].map((option, optionIndex)=>{\n                                                                const isSelected = userAnswers[exercise.id] === optionIndex;\n                                                                const isCorrect = optionIndex === exercise.correctAnswer;\n                                                                const showResult = userAnswers[exercise.id] !== undefined;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleExerciseAnswer(exercise.id, optionIndex),\n                                                                    className: \"w-full text-left p-4 rounded-lg border-2 transition-all duration-200 font-medium \".concat(showResult && isCorrect ? 'bg-green-100 border-green-500 text-green-700 shadow-md' : showResult && isSelected && !isCorrect ? 'bg-red-100 border-red-500 text-red-700 shadow-md' : isSelected ? 'bg-secondary-30/10 border-secondary-30 text-secondary-dark shadow-md' : 'bg-white border-gray-300 hover:bg-gray-50 hover:border-gray-400'),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-6 h-6 rounded-full border-2 flex items-center justify-center text-sm font-bold \".concat(showResult && isCorrect ? 'bg-green-500 border-green-500 text-white' : showResult && isSelected && !isCorrect ? 'bg-red-500 border-red-500 text-white' : isSelected ? 'bg-blue-500 border-blue-500 text-white' : 'border-gray-400 text-gray-600'),\n                                                                                children: optionIndex === 0 ? 'T' : 'F'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: option\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            showResult && isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-auto text-green-600\",\n                                                                                children: \"✓\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 37\n                                                                            }, this),\n                                                                            showResult && isSelected && !isCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-auto text-red-600\",\n                                                                                children: \"✗\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                                lineNumber: 361,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, optionIndex, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 31\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        exercise.type === 'fill-blank' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: userAnswers[exercise.id] || '',\n                                                                    onChange: (e)=>handleExerciseAnswer(exercise.id, e.target.value),\n                                                                    placeholder: \"Type your answer here...\",\n                                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                userAnswers[exercise.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-4 rounded-lg border-l-4 \".concat(userAnswers[exercise.id].toLowerCase().trim() === ((_exercise_answer = exercise.answer) === null || _exercise_answer === void 0 ? void 0 : _exercise_answer.toLowerCase().trim()) ? 'bg-green-50 text-green-700 border-green-500' : 'bg-red-50 text-red-700 border-red-500'),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xl\",\n                                                                                children: userAnswers[exercise.id].toLowerCase().trim() === ((_exercise_answer1 = exercise.answer) === null || _exercise_answer1 === void 0 ? void 0 : _exercise_answer1.toLowerCase().trim()) ? '✅' : '❌'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                                lineNumber: 386,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: userAnswers[exercise.id].toLowerCase().trim() === ((_exercise_answer2 = exercise.answer) === null || _exercise_answer2 === void 0 ? void 0 : _exercise_answer2.toLowerCase().trim()) ? 'Correct!' : \"Incorrect. The correct answer is: \".concat(exercise.answer)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                                lineNumber: 391,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        userAnswers[exercise.id] !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 p-4 bg-blue-50 border-l-4 border-blue-500 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-shrink-0 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mt-0.5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white text-xs\",\n                                                                            children: \"\\uD83D\\uDCA1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                className: \"font-medium text-blue-800 mb-2\",\n                                                                                children: \"Explanation:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                content: exercise.explanation,\n                                                                                className: \"text-blue-700\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                                lineNumber: 411,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, exercise.id, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this),\n                                        currentSection.type === 'summary' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-700 leading-relaxed mb-6\",\n                                                    children: currentSection.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        content: currentSection.content,\n                                                        className: \"text-gray-700 leading-relaxed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, this),\n                                                currentSection.keyPoints && currentSection.keyPoints.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-800 mb-3\",\n                                                            children: \"Key Points:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-3\",\n                                                            children: currentSection.keyPoints.map((point, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-500 mr-3 mt-1 text-lg\",\n                                                                            children: \"✓\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                            lineNumber: 441,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                content: point,\n                                                                                className: \"text-gray-700\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                                lineNumber: 443,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                            lineNumber: 442,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: prevSection,\n                                            disabled: currentSectionIndex === 0,\n                                            className: \"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                            children: \"← Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Section \",\n                                                    currentSectionIndex + 1,\n                                                    \" of \",\n                                                    selectedLesson.sections.length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this),\n                                        currentSectionIndex < selectedLesson.sections.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: nextSection,\n                                            className: \"px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-colors\",\n                                            children: \"Next →\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedLesson(null),\n                                            className: \"px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\",\n                                            children: \"Complete Lesson ✓\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdSense__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                adSlot: \"2345678901\",\n                                adFormat: \"auto\",\n                                className: \"rounded-lg\",\n                                style: {\n                                    display: 'block',\n                                    minHeight: '100px'\n                                },\n                                lazy: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isOpen: showAuthModal,\n                    onClose: ()=>setShowAuthModal(false)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                    lineNumber: 503,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onAuthClick: ()=>setShowAuthModal(true),\n                showBackButton: true,\n                onBackClick: ()=>window.history.back()\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                lineNumber: 513,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24 pb-8 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl sm:text-5xl pb-2 font-bold bg-gradient-to-r from-gray-800 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-4\",\n                                    children: t('lessonTitle')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto mb-6\",\n                                    children: t('lessonSubtitle')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-8 mb-8 shadow-xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-800 mb-6\",\n                                    children: t('createLesson')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: t('level')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: selectedLevel,\n                                                    onChange: (e)=>setSelectedLevel(e.target.value),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                    children: levels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: level,\n                                                            children: level.charAt(0).toUpperCase() + level.slice(1)\n                                                        }, level, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: t('lessonType')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: selectedType,\n                                                    onChange: (e)=>setSelectedType(e.target.value),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                    children: lessonTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type.id,\n                                                            children: type.name\n                                                        }, type.id, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: t('duration')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: duration,\n                                                    onChange: (e)=>setDuration(parseInt(e.target.value)),\n                                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 15,\n                                                            children: \"15 minutes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 30,\n                                                            children: \"30 minutes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 45,\n                                                            children: \"45 minutes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 60,\n                                                            children: \"60 minutes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: generateLesson,\n                                                disabled: isGenerating || !topic.trim(),\n                                                className: \"w-full px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium\",\n                                                children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        t('generating')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 21\n                                                }, this) : \"✨ \".concat(t('generateLesson'))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Lesson Topic *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: topic,\n                                            onChange: (e)=>setTopic(e.target.value),\n                                            placeholder: \"e.g., Present Simple Tense, Business English, Travel Vocabulary...\",\n                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Or choose from popular topics:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: topicSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setTopic(suggestion),\n                                                    className: \"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm transition-colors\",\n                                                    children: suggestion\n                                                }, index, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-6 mb-8 shadow-xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                    children: \"Lesson Types\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: lessonTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 rounded-lg border-2 transition-all cursor-pointer \".concat(selectedType === type.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'),\n                                            onClick: ()=>setSelectedType(type.id),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: type.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-800\",\n                                                                children: type.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: type.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, type.id, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                            lineNumber: 635,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                    children: \"How AI Lessons Work:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-600 font-bold\",\n                                                        children: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-800 mb-2\",\n                                                    children: \"Choose Topic\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Select your lesson topic and preferences\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-600 font-bold\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-800 mb-2\",\n                                                    children: t('aiCreates')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: t('aiCreatesDesc')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 font-bold\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-800 mb-2\",\n                                                    children: \"Interactive Learning\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Study with explanations and exercises\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-orange-600 font-bold\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-800 mb-2\",\n                                                    children: \"Practice & Master\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Complete exercises and track progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                    children: \"Popular Learning Topics\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gray-100 px-3 py-1 rounded-full text-gray-700\",\n                                            children: \"Grammar Fundamentals\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gray-100 px-3 py-1 rounded-full text-gray-700\",\n                                            children: \"Business English\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gray-100 px-3 py-1 rounded-full text-gray-700\",\n                                            children: \"Conversation Skills\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gray-100 px-3 py-1 rounded-full text-gray-700\",\n                                            children: \"IELTS Preparation\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gray-100 px-3 py-1 rounded-full text-gray-700\",\n                                            children: \"Academic Writing\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gray-100 px-3 py-1 rounded-full text-gray-700\",\n                                            children: \"Pronunciation\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gray-100 px-3 py-1 rounded-full text-gray-700\",\n                                            children: \"Vocabulary Building\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gray-100 px-3 py-1 rounded-full text-gray-700\",\n                                            children: \"Cultural Context\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                            lineNumber: 699,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                    lineNumber: 520,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                lineNumber: 519,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                lineNumber: 715,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n                lineNumber: 720,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\LessonsClient.tsx\",\n        lineNumber: 512,\n        columnNumber: 5\n    }, this);\n}\n_s(LessonsClient, \"/B1f6+9SatKhk/Ai61P5poJIbiQ=\", false, function() {\n    return [\n        _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage\n    ];\n});\n_c = LessonsClient;\nvar _c;\n$RefreshReg$(_c, \"LessonsClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LessonsClient.tsx\n"));

/***/ })

});